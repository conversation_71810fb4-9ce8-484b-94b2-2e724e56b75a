#!/usr/bin/env python3
"""
Test script to validate the machine learning model comparison implementation
"""

import pandas as pd
import numpy as np
import os
import sys
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import warnings
warnings.filterwarnings('ignore')

def test_data_loading():
    """Test if data loads correctly"""
    print("Testing data loading...")
    try:
        data = pd.read_excel("bp_ai.xlsx")
        assert data.shape[0] > 0, "Dataset is empty"
        assert 'hypot_map' in data.columns, "Target variable 'hypot_map' not found"
        assert 'arterial_map' in data.columns, "arterial_map column should exist before removal"
        print("✓ Data loading test passed")
        return True
    except Exception as e:
        print(f"✗ Data loading test failed: {e}")
        return False

def test_preprocessing():
    """Test data preprocessing"""
    print("Testing data preprocessing...")
    try:
        data = pd.read_excel("bp_ai.xlsx")
        
        # Remove arterial_map
        data = data.drop('arterial_map', axis=1)
        assert 'arterial_map' not in data.columns, "arterial_map should be removed"
        
        # Check target variable
        X = data.drop('hypot_map', axis=1)
        y = data['hypot_map']
        
        assert X.shape[1] == 10, f"Expected 10 features, got {X.shape[1]}"
        assert len(y.unique()) == 2, "Target should be binary"
        assert set(y.unique()) == {'No', 'Yes'}, "Target values should be 'No' and 'Yes'"
        
        # Test label encoding
        le = LabelEncoder()
        y_encoded = le.fit_transform(y)
        assert set(y_encoded) == {0, 1}, "Encoded labels should be 0 and 1"
        
        print("✓ Data preprocessing test passed")
        return True
    except Exception as e:
        print(f"✗ Data preprocessing test failed: {e}")
        return False

def test_train_test_split():
    """Test train-test split"""
    print("Testing train-test split...")
    try:
        data = pd.read_excel("bp_ai.xlsx")
        data = data.drop('arterial_map', axis=1)
        X = data.drop('hypot_map', axis=1)
        y = data['hypot_map']
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y)
        
        # Check split proportions
        assert len(X_train) + len(X_test) == len(X), "Split sizes don't add up"
        assert abs(len(X_test) / len(X) - 0.2) < 0.01, "Test size not approximately 20%"
        
        # Check stratification
        train_prop = (y_train == 'Yes').mean()
        test_prop = (y_test == 'Yes').mean()
        overall_prop = (y == 'Yes').mean()
        
        assert abs(train_prop - overall_prop) < 0.05, "Training set not properly stratified"
        assert abs(test_prop - overall_prop) < 0.05, "Test set not properly stratified"
        
        print("✓ Train-test split test passed")
        return True
    except Exception as e:
        print(f"✗ Train-test split test failed: {e}")
        return False

def test_file_generation():
    """Test if required files are generated"""
    print("Testing file generation...")
    required_files = [
        'model_performance_summary.csv',
        'feature_importance.csv',
        'roc_curves.png',
        'confusion_matrices.png',
        'feature_importance.png',
        'model_comparison_metrics.png'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"✗ File generation test failed. Missing files: {missing_files}")
        return False
    else:
        print("✓ File generation test passed")
        return True

def test_results_validity():
    """Test if results are valid"""
    print("Testing results validity...")
    try:
        # Check CSV files
        summary_df = pd.read_csv('model_performance_summary.csv')
        
        # Check if all metrics are in valid ranges
        assert (summary_df['Accuracy'] >= 0).all() and (summary_df['Accuracy'] <= 1).all(), "Invalid accuracy values"
        assert (summary_df['Precision'] >= 0).all() and (summary_df['Precision'] <= 1).all(), "Invalid precision values"
        assert (summary_df['Recall'] >= 0).all() and (summary_df['Recall'] <= 1).all(), "Invalid recall values"
        assert (summary_df['F1-Score'] >= 0).all() and (summary_df['F1-Score'] <= 1).all(), "Invalid F1-score values"
        assert (summary_df['ROC-AUC'] >= 0).all() and (summary_df['ROC-AUC'] <= 1).all(), "Invalid ROC-AUC values"
        
        # Check if we have results for expected models
        expected_models = ['Neural Network (Small)', 'Random Forest', 'XGBoost', 'Logistic Regression']
        for model in expected_models:
            assert model in summary_df['Model'].values, f"Missing results for {model}"
        
        # Check feature importance
        fi_df = pd.read_csv('feature_importance.csv')
        assert (fi_df['importance'] >= 0).all(), "Feature importance should be non-negative"
        assert abs(fi_df['importance'].sum() - 1.0) < 0.01, "Feature importance should sum to approximately 1"
        
        print("✓ Results validity test passed")
        return True
    except Exception as e:
        print(f"✗ Results validity test failed: {e}")
        return False

def test_model_performance():
    """Test if model performance is reasonable"""
    print("Testing model performance...")
    try:
        summary_df = pd.read_csv('model_performance_summary.csv')
        
        # Check if best model has reasonable performance
        best_model = summary_df.loc[summary_df['ROC-AUC'].idxmax()]
        
        assert best_model['Accuracy'] > 0.7, f"Best model accuracy too low: {best_model['Accuracy']}"
        assert best_model['ROC-AUC'] > 0.7, f"Best model ROC-AUC too low: {best_model['ROC-AUC']}"
        
        # Check if neural network is among top performers
        nn_models = summary_df[summary_df['Model'].str.contains('Neural Network')]
        assert len(nn_models) > 0, "No neural network models found"
        
        best_nn = nn_models.loc[nn_models['ROC-AUC'].idxmax()]
        assert best_nn['ROC-AUC'] > 0.8, f"Best neural network ROC-AUC too low: {best_nn['ROC-AUC']}"
        
        print("✓ Model performance test passed")
        return True
    except Exception as e:
        print(f"✗ Model performance test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("="*60)
    print("RUNNING MODEL COMPARISON TESTS")
    print("="*60)
    
    tests = [
        test_data_loading,
        test_preprocessing,
        test_train_test_split,
        test_file_generation,
        test_results_validity,
        test_model_performance
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("="*60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print("="*60)
    
    if passed == total:
        print("🎉 All tests passed! The model comparison is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

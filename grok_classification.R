# Load required libraries
library(tidyverse)
library(caret)
library(randomForest)
library(neuralnet)
library(readxl)

data = read_excel("bp_ai.xlsx")

# Remove arterial_map as specified and keep hypot_map as target
data <- data %>% select(-arterial_map)

# Convert hypot_map to factor for classification
data$hypot_map <- as.factor(data$hypot_map)

# Split data into training and testing sets
set.seed(123)
trainIndex <- createDataPartition(data$hypot_map, p = 0.8, list = FALSE)
train_data <- data[trainIndex, ]
test_data <- data[-trainIndex, ]

# Create dummy variables for categorical predictors (excluding hypot_map)
dummy_model <- dummyVars(" ~ gender + asa + hypothyroid + dm", 
                         data = train_data,
                         fullRank = TRUE)
train_dummies <- predict(dummy_model, train_data)
test_dummies <- predict(dummy_model, test_data)

# Define numeric predictors
numeric_vars <- c("age", "bmi", "arm_circum", "ankle_circum", "arm_map", "ankle_map")

# Separate numeric predictors and target variable
train_data_numeric <- train_data[, numeric_vars]
test_data_numeric <- test_data[, numeric_vars]
train_target <- train_data$hypot_map
test_target <- test_data$hypot_map

# Combine numeric predictors with dummy variables
train_data_processed <- cbind(train_data_numeric, train_dummies)
test_data_processed <- cbind(test_data_numeric, test_dummies)

# Scale numeric predictor variables only
preProcess <- preProcess(train_data_processed[, numeric_vars], 
                         method = c("center", "scale"))
train_data_processed <- predict(preProcess, train_data_processed)
test_data_processed <- predict(preProcess, test_data_processed)

# Add target variable back to training data
train_data_processed$hypot_map <- train_target
test_data_processed$hypot_map <- test_target

# Define training control for classification
ctrl <- trainControl(method = "cv", 
                     number = 10,
                     classProbs = TRUE,
                     summaryFunction = twoClassSummary)

# 1. Logistic Regression Model
lr_model <- train(hypot_map ~ .,
                  data = train_data_processed,
                  method = "glm",
                  family = "binomial",
                  trControl = ctrl,
                  metric = "ROC")

# 2. Random Forest Model
rf_model <- train(hypot_map ~ .,
                  data = train_data_processed,
                  method = "rf",
                  trControl = ctrl,
                  tuneLength = 5,
                  metric = "ROC")

# 3. Neural Network Model
# Create formula with all predictors (excluding hypot_map)
predictors <- names(train_data_processed)[!names(train_data_processed) %in% "hypot_map"]
formula <- as.formula(paste("hypot_map ~", paste(predictors, collapse = " + ")))

# For neuralnet, we need numeric output (0/1), so create a binary version
train_data_nn <- train_data_processed
train_data_nn$hypot_map <- as.numeric(train_data_processed$hypot_map == "Yes")
test_data_nn <- test_data_processed
test_data_nn$hypot_map <- as.numeric(test_data_processed$hypot_map == "Yes")

nn_model <- neuralnet(formula,
                      data = train_data_nn,
                      hidden = c(15, 10),
                      linear.output = FALSE,  # Classification problem
                      stepmax = 1e6,
                      lifesign = 'full',
                      lifesign.step = 200,
                      threshold = 0.005)

# Make predictions
lr_pred <- predict(lr_model, newdata = test_data_processed, type = "raw")
rf_pred <- predict(rf_model, newdata = test_data_processed, type = "raw")
nn_pred_raw <- compute(nn_model, test_data_processed[, predictors])$net.result
nn_pred <- ifelse(nn_pred_raw > 0.5, "Yes", "No")
nn_pred <- factor(nn_pred, levels = c("No", "Yes"))

# Evaluate models
evaluate_model <- function(predictions, actual) {
  cm <- confusionMatrix(predictions, actual)
  accuracy <- cm$overall["Accuracy"]
  sensitivity <- cm$byClass["Sensitivity"]
  specificity <- cm$byClass["Specificity"]
  return(list(Accuracy = accuracy, 
              Sensitivity = sensitivity, 
              Specificity = specificity))
}

# Get evaluation metrics
lr_metrics <- evaluate_model(lr_pred, test_target)
rf_metrics <- evaluate_model(rf_pred, test_target)
nn_metrics <- evaluate_model(nn_pred, test_target)

# Print results
cat("Logistic Regression Results:\n")
cat("Accuracy:", lr_metrics$Accuracy, "\n")
cat("Sensitivity:", lr_metrics$Sensitivity, "\n")
cat("Specificity:", lr_metrics$Specificity, "\n\n")

cat("Random Forest Results:\n")
cat("Accuracy:", rf_metrics$Accuracy, "\n")
cat("Sensitivity:", rf_metrics$Sensitivity, "\n")
cat("Specificity:", rf_metrics$Specificity, "\n\n")

cat("Neural Network Results:\n")
cat("Accuracy:", nn_metrics$Accuracy, "\n")
cat("Sensitivity:", nn_metrics$Sensitivity, "\n")
cat("Specificity:", nn_metrics$Specificity, "\n")

# Feature importance for Random Forest
importance <- varImp(rf_model)
print(importance)

# Plot confusion matrices
par(mfrow = c(1, 3))
plot(confusionMatrix(lr_pred, test_target)$table, main = "Logistic Regression")
plot(confusionMatrix(rf_pred, test_target)$table, main = "Random Forest")
plot(confusionMatrix(nn_pred, test_target)$table, main = "Neural Network")

library(knitr)

# Create results table
results_table <- data.frame(
  Model = c("Logistic Regression", "Random Forest", "Neural Network"),
  Accuracy = c(lr_metrics$Accuracy, rf_metrics$Accuracy, nn_metrics$Accuracy),
  Sensitivity = c(lr_metrics$Sensitivity, rf_metrics$Sensitivity, nn_metrics$Sensitivity),
  Specificity = c(lr_metrics$Specificity, rf_metrics$Specificity, nn_metrics$Specificity)
)

# Round numeric values to 3 decimal places
results_table[, 2:4] <- round(results_table[, 2:4], 3)

# Print the table
cat("\nModel Performance Comparison:\n")
print(kable(results_table, format = "markdown", 
            caption = "Classification Model Performance Metrics",
            align = "c"))


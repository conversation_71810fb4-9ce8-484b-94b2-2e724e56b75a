tuneLength = 5,
metric = "ROC")
# 3. Neural Network Model
# Create formula with all predictors (excluding hypot_map)
predictors <- names(train_data_processed)[!names(train_data_processed) %in% "hypot_map"]
formula <- as.formula(paste("hypot_map ~", paste(predictors, collapse = " + ")))
# For neuralnet, we need numeric output (0/1), so create a binary version
train_data_nn <- train_data_processed
train_data_nn$hypot_map <- as.numeric(train_data_processed$hypot_map == "Yes")
test_data_nn <- test_data_processed
test_data_nn$hypot_map <- as.numeric(test_data_processed$hypot_map == "Yes")
nn_model <- neuralnet(formula,
data = train_data_nn,
hidden = c(15, 10),
linear.output = FALSE,  # Classification problem
stepmax = 1e6)
nn_model <- neuralnet(formula,
data = train_data_nn,
hidden = c(15, 10),
linear.output = FALSE,  # Classification problem
stepmax = 1e6,
lifesign = 'full',
lifesign.step = 100)
nn_model <- neuralnet(formula,
data = train_data_nn,
hidden = c(15, 10),
linear.output = FALSE,  # Classification problem
stepmax = 1e6,
lifesign = 'full',
lifesign.step = 500,
threshold = 0.01)
# Make predictions
lr_pred <- predict(lr_model, newdata = test_data_processed, type = "raw")
rf_pred <- predict(rf_model, newdata = test_data_processed, type = "raw")
nn_pred_raw <- compute(nn_model, test_data_processed[, predictors])$net.result
nn_pred <- ifelse(nn_pred_raw > 0.5, "Yes", "No")
nn_pred <- factor(nn_pred, levels = c("No", "Yes"))
# Evaluate models
evaluate_model <- function(predictions, actual) {
cm <- confusionMatrix(predictions, actual)
accuracy <- cm$overall["Accuracy"]
sensitivity <- cm$byClass["Sensitivity"]
specificity <- cm$byClass["Specificity"]
return(list(Accuracy = accuracy,
Sensitivity = sensitivity,
Specificity = specificity))
}
# Get evaluation metrics
lr_metrics <- evaluate_model(lr_pred, test_target)
rf_metrics <- evaluate_model(rf_pred, test_target)
nn_metrics <- evaluate_model(nn_pred, test_target)
# Print results
cat("Logistic Regression Results:\n")
cat("Accuracy:", lr_metrics$Accuracy, "\n")
cat("Sensitivity:", lr_metrics$Sensitivity, "\n")
cat("Specificity:", lr_metrics$Specificity, "\n\n")
cat("Random Forest Results:\n")
cat("Accuracy:", rf_metrics$Accuracy, "\n")
cat("Sensitivity:", rf_metrics$Sensitivity, "\n")
cat("Specificity:", rf_metrics$Specificity, "\n\n")
cat("Neural Network Results:\n")
cat("Accuracy:", nn_metrics$Accuracy, "\n")
cat("Sensitivity:", nn_metrics$Sensitivity, "\n")
cat("Specificity:", nn_metrics$Specificity, "\n")
# Feature importance for Random Forest
importance <- varImp(rf_model)
print(importance)
# Plot confusion matrices
par(mfrow = c(1, 3))
plot(confusionMatrix(lr_pred, test_target)$table, main = "Logistic Regression")
plot(confusionMatrix(rf_pred, test_target)$table, main = "Random Forest")
plot(confusionMatrix(nn_pred, test_target)$table, main = "Neural Network")
nn_model <- neuralnet(formula,
data = train_data_nn,
hidden = c(15, 10),
linear.output = FALSE,  # Classification problem
stepmax = 1e6,
lifesign = 'full',
lifesign.step = 200,
threshold = 0.005)
# Make predictions
lr_pred <- predict(lr_model, newdata = test_data_processed, type = "raw")
rf_pred <- predict(rf_model, newdata = test_data_processed, type = "raw")
nn_pred_raw <- compute(nn_model, test_data_processed[, predictors])$net.result
nn_pred <- ifelse(nn_pred_raw > 0.5, "Yes", "No")
nn_pred <- factor(nn_pred, levels = c("No", "Yes"))
# Evaluate models
evaluate_model <- function(predictions, actual) {
cm <- confusionMatrix(predictions, actual)
accuracy <- cm$overall["Accuracy"]
sensitivity <- cm$byClass["Sensitivity"]
specificity <- cm$byClass["Specificity"]
return(list(Accuracy = accuracy,
Sensitivity = sensitivity,
Specificity = specificity))
}
# Get evaluation metrics
lr_metrics <- evaluate_model(lr_pred, test_target)
rf_metrics <- evaluate_model(rf_pred, test_target)
nn_metrics <- evaluate_model(nn_pred, test_target)
# Print results
cat("Logistic Regression Results:\n")
cat("Accuracy:", lr_metrics$Accuracy, "\n")
cat("Sensitivity:", lr_metrics$Sensitivity, "\n")
cat("Specificity:", lr_metrics$Specificity, "\n\n")
cat("Random Forest Results:\n")
cat("Accuracy:", rf_metrics$Accuracy, "\n")
cat("Sensitivity:", rf_metrics$Sensitivity, "\n")
cat("Specificity:", rf_metrics$Specificity, "\n\n")
cat("Neural Network Results:\n")
cat("Accuracy:", nn_metrics$Accuracy, "\n")
cat("Sensitivity:", nn_metrics$Sensitivity, "\n")
cat("Specificity:", nn_metrics$Specificity, "\n")
# Feature importance for Random Forest
importance <- varImp(rf_model)
print(importance)
# Plot confusion matrices
par(mfrow = c(1, 3))
plot(confusionMatrix(lr_pred, test_target)$table, main = "Logistic Regression")
plot(confusionMatrix(rf_pred, test_target)$table, main = "Random Forest")
plot(confusionMatrix(nn_pred, test_target)$table, main = "Neural Network")
library(readxl)
bp <- read_excel("bp_ai.xlsx")
str(bp)
#bp$gender = as.factor(bp$gender)
bp$gender <- ifelse(bp$gender == "Male", 0, 1)
#bp$asa = as.factor(bp$asa)
bp$asa <- ifelse(bp$asa == "I", 1, 2)
#bp$hypothyroid = as.factor(bp$hypothyroid)
bp$hypothyroid <- ifelse(bp$hypothyroid == "Yes", 1, 0)
#bp$dm = as.factor(bp$dm)
bp$dm <- ifelse(bp$dm == "Yes", 1, 0)
bp$hypot_map = ifelse(bp$hypot_map == "Yes", 1, 0)
str(bp)
library(tidyverse)
# Definetidyverse# Define all predictors (continuous and categorical combined)
X_all <- bp[,-c(which(names(bp) == "arterial_map"),
which(names(bp) == "hypot_map"))] %>% as.matrix()
#classification problem
y <- bp$hypot_map
y_train <- y[train_idx]
y_test <- y[-train_idx]
# Split into training (80%) and testing (20%) sets
set.seed(103)
train_idx <- sample(1:nrow(bp), 0.8 * nrow(bp))
X_all_train <- X_all[train_idx, ]
X_all_test <- X_all[-train_idx, ]
y_train <- y[train_idx]
y_test <- y[-train_idx]
# Normalize continuous variables only
X_continuous_mean <- apply(X_all_train[, c(1,2,7,8,9,10)], 2, mean)
X_continuous_sd <- apply(X_all_train[, c(1,2,7,8,9,10)], 2, sd)
X_all_train_normalized <- X_all_train
X_all_test_normalized <- X_all_test
X_all_train_normalized[, c(1,2,7,8,9,10)] <- scale(X_all_train[, c(1,2,7,8,9,10)],
center = X_continuous_mean, scale = X_continuous_sd)
X_all_test_normalized[, c(1,2,7,8,9,10)] <- scale(X_all_test[, c(1,2,7,8,9,10)],
center = X_continuous_mean, scale = X_continuous_sd)
library(keras)
# Define the neural network using the functional API
input_all <- layer_input(shape = ncol(X_all_train_normalized), name = "all_input")
#classification problem
y <- bp$hypot_map
y_train <- y[train_idx]
y_test <- y[-train_idx]
library(keras)
# Define the neural network using the functional API
input_all <- layer_input(shape = ncol(X_all_train_normalized), name = "all_input")
# First hidden layer: 100 neurons, fully connected to all inputs
hidden_layer1 <- input_all %>%
layer_dense(units = 80, activation = "relu", name = "hidden_layer1")
# Second hidden layer: 20 neurons
hidden_layer2 <- hidden_layer1 %>%
layer_dense(units = 40, activation = "relu", name = "hidden_layer2")
# Output layer
output <- hidden_layer2 %>%
layer_dense(units = 1, activation = "sigmoid", name = "output")
# Define the model
model <- keras_model(inputs = input_all, outputs = output)
# Compile the model
model %>% compile(
optimizer = "adam",
loss = "binary_crossentropy",  # Binary classification loss
metrics = c("accuracy")
)
# Train the model
history <- model %>% fit(
x = X_all_train_normalized,
y = y_train,
epochs = 50,
batch_size = 32,
validation_split = 0.2,
verbose = 1
)
# Evaluate on test set
# Evaluate on test data
score <- model %>% evaluate(X_all_test_normalized, y_test)
cat("Test Accuracy:", score[[2]])
# Get predicted probabilities
y_pred_prob <- model %>% predict(X_all_test_normalized)
# Convert probabilities to class labels (0 or 1) using a threshold of 0.5
y_pred <- ifelse(y_pred_prob > 0.5, 1, 0)
# Create confusion matrix
conf_matrix <- table(Predicted = y_pred, Actual = y_test)
print(conf_matrix)
library(caret)
# Convert to factor for caret compatibility
y_pred_factor <- factor(y_pred, levels = c(0,1))
y_test_factor <- factor(y_test, levels = c(0,1))
# Generate confusion matrix
confusionMatrix(y_pred_factor, y_test_factor)
# Load necessary libraries
library(caret)
library(readxl)
data <- read_excel("bp_ML.xlsx")
# Check the data structure
str(data)
summary(data)
# Remove rows with missing values (if any)
data <- na.omit(data)
# Define the dependent and independent variables
set.seed(123)
trainIndex <- createDataPartition(data$Arterial, p = 0.8, list = FALSE)
trainData <- data[trainIndex, ]
testData <- data[-trainIndex, ]
# Define the training control
trainControl <- trainControl(method = "cv", number = 10)
# Build a random forest model
model <- train(Arterial ~ ., data = trainData,
method = "rf", trControl = trainControl)
# Summary of the model
print(model)
# Make predictions on the test set
predictions <- predict(model, newdata = testData)
# Evaluate model performance
results <- postResample(predictions, testData$Arterial)
print(results)
# Plot actual vs predicted values
plot(testData$Arterial, predictions,
main = "Actual vs Predicted Arterial Values",
xlab = "Actual Values", ylab = "Predicted Values")
abline(0, 1, col = "red")
# Load necessary libraries
library(caret)
library(readxl)
data <- read_excel("bp_ai.xlsx")
data$gender <- ifelse(data$gender == "Male", 0, 1)
data$asa <- ifelse(data$asa == "I", 1, 2)
data$hypothyroid <- ifelse(data$hypothyroid == "Yes", 1, 0)
data$dm <- ifelse(data$dm == "Yes", 1, 0)
data$hypot_map = ifelse(data$hypot_map == "Yes", 1, 0)
X_all <- data[,-c(which(names(bp) == "arterial_map"),
which(names(bp) == "hypot_map"))]
y = data$arterial_map
# Split into training (80%) and testing (20%) sets
set.seed(103)
train_idx <- sample(1:nrow(bp), 0.8 * nrow(bp))
X_all_train <- X_all[train_idx, ]
X_all_test <- X_all[-train_idx, ]
y_train <- y[train_idx]
y_test <- y[-train_idx]
# Normalize continuous variables only
X_continuous_mean <- apply(X_all_train[, c(1,2,7,8,9,10)], 2, mean)
X_continuous_sd <- apply(X_all_train[, c(1,2,7,8,9,10)], 2, sd)
X_all_train_normalized <- X_all_train
X_all_test_normalized <- X_all_test
X_all_train_normalized[, c(1,2,7,8,9,10)] <- scale(X_all_train[, c(1,2,7,8,9,10)],
center = X_continuous_mean, scale = X_continuous_sd)
X_all_test_normalized[, c(1,2,7,8,9,10)] <- scale(X_all_test[, c(1,2,7,8,9,10)],
center = X_continuous_mean, scale = X_continuous_sd)
ml_data = data.frame(X_all_train_normalized, y_train)
# Define the training control
trainControl <- trainControl(method = "cv", number = 10)
# Build a random forest regression model
model <- train(y_train ~ ., data = ml_data,
method = "rf", trControl = trainControl)
# Summary of the model
print(model)
# Make predictions on the test set
predictions <- predict(model, newdata = X_all_test_normalized)
# Evaluate model performance
results <- postResample(predictions, y_test)
print(results)
# Plot actual vs predicted values
plot(y_test, predictions,
main = "Actual vs Predicted Arterial Values",
xlab = "Actual Values", ylab = "Predicted Values")
abline(0, 1, col = "red")
#Classification model
y = as.factor(data$hypot_map)
y_train <- y[train_idx]
y_test <- y[-train_idx]
ml_data = data.frame(X_all_train_normalized, y_train)
model <- train(y_train ~ ., data = ml_data,
method = "rf", trControl = trainControl)
print(model)
# Predict on test data
predictions <- predict(model, newdata = X_all_test_normalized)
# View first few predictions
head(predictions)
# Generate confusion matrix
conf_matrix <- confusionMatrix(predictions, y_test)
print(conf_matrix)
# Plot feature importance
library(randomForest)
varImpPlot(model$finalModel)
library(knitr)
# Create results table
results_table <- data.frame(
Model = c("Logistic Regression", "Random Forest", "Neural Network"),
Accuracy = c(lr_metrics$Accuracy, rf_metrics$Accuracy, nn_metrics$Accuracy),
Sensitivity = c(lr_metrics$Sensitivity, rf_metrics$Sensitivity, nn_metrics$Sensitivity),
Specificity = c(lr_metrics$Specificity, rf_metrics$Specificity, nn_metrics$Specificity)
)
# Round numeric values to 3 decimal places
results_table[, 2:4] <- round(results_table[, 2:4], 3)
# Print the table
cat("\nModel Performance Comparison:\n")
print(kable(results_table, format = "markdown",
caption = "Classification Model Performance Metrics",
align = "c"))
library(readxl)
bp <- read_excel("bp_ai.xlsx")
str(bp)
#bp$gender = as.factor(bp$gender)
bp$gender <- ifelse(bp$gender == "Male", 0, 1)
#bp$asa = as.factor(bp$asa)
bp$asa <- ifelse(bp$asa == "I", 1, 2)
#bp$hypothyroid = as.factor(bp$hypothyroid)
bp$hypothyroid <- ifelse(bp$hypothyroid == "Yes", 1, 0)
#bp$dm = as.factor(bp$dm)
bp$dm <- ifelse(bp$dm == "Yes", 1, 0)
bp$hypot_map = ifelse(bp$hypot_map == "Yes", 1, 0)
str(bp)
library(tidyverse)
# Definetidyverse# Define all predictors (continuous and categorical combined)
X_all <- bp[,-c(which(names(bp) == "arterial_map"),
which(names(bp) == "hypot_map"))] %>% as.matrix()
y <- bp$arterial_map
# Split into training (80%) and testing (20%) sets
set.seed(103)
train_idx <- sample(1:nrow(bp), 0.8 * nrow(bp))
X_all_train <- X_all[train_idx, ]
X_all_test <- X_all[-train_idx, ]
y_train <- y[train_idx]
y_test <- y[-train_idx]
# Normalize continuous variables only
X_continuous_mean <- apply(X_all_train[, c(1,2,7,8,9,10)], 2, mean)
X_continuous_sd <- apply(X_all_train[, c(1,2,7,8,9,10)], 2, sd)
X_all_train_normalized <- X_all_train
X_all_test_normalized <- X_all_test
X_all_train_normalized[, c(1,2,7,8,9,10)] <- scale(X_all_train[, c(1,2,7,8,9,10)],
center = X_continuous_mean, scale = X_continuous_sd)
X_all_test_normalized[, c(1,2,7,8,9,10)] <- scale(X_all_test[, c(1,2,7,8,9,10)],
center = X_continuous_mean, scale = X_continuous_sd)
library(keras)
# Define the neural network using the functional API
input_all <- layer_input(shape = ncol(X_all_train_normalized), name = "all_input")
# First hidden layer: 100 neurons, fully connected to all inputs
hidden_layer1 <- input_all %>%
layer_dense(units = 80, activation = "relu", name = "hidden_layer1")
# Second hidden layer: 20 neurons
hidden_layer2 <- hidden_layer1 %>%
layer_dense(units = 40, activation = "relu", name = "hidden_layer2")
# Output layer
output <- hidden_layer2 %>%
layer_dense(units = 1, activation = "linear", name = "output")
# Define the model
model <- keras_model(inputs = input_all, outputs = output)
# Compile the model
model %>% compile(
optimizer = "adam",
loss = "mean_squared_error",
metrics = c("mean_absolute_error")
)
# Train the model
history <- model %>% fit(
x = X_all_train_normalized,
y = y_train,
epochs = 50,
batch_size = 32,
validation_split = 0.2,
verbose = 1
)
# Evaluate on test set
evaluation <- model %>% evaluate(
x = X_all_test_normalized,
y = y_test,
verbose = 0
)
cat("Neural Network (Fully Connected) - Test MSE:", evaluation[[1]], "\n")
cat("Neural Network (Fully Connected) - Test MAE:", evaluation[[2]], "\n")
#classification problem
y <- bp$hypot_map
y_train <- y[train_idx]
y_test <- y[-train_idx]
library(keras)
# Define the neural network using the functional API
input_all <- layer_input(shape = ncol(X_all_train_normalized), name = "all_input")
# First hidden layer: 100 neurons, fully connected to all inputs
hidden_layer1 <- input_all %>%
layer_dense(units = 80, activation = "relu", name = "hidden_layer1")
# Second hidden layer: 20 neurons
hidden_layer2 <- hidden_layer1 %>%
layer_dense(units = 40, activation = "relu", name = "hidden_layer2")
# Output layer
output <- hidden_layer2 %>%
layer_dense(units = 1, activation = "sigmoid", name = "output")
# Define the model
model <- keras_model(inputs = input_all, outputs = output)
# Compile the model
model %>% compile(
optimizer = "adam",
loss = "binary_crossentropy",  # Binary classification loss
metrics = c("accuracy")
)
# Train the model
history <- model %>% fit(
x = X_all_train_normalized,
y = y_train,
epochs = 50,
batch_size = 32,
validation_split = 0.2,
verbose = 1
)
# Evaluate on test set
# Evaluate on test data
score <- model %>% evaluate(X_all_test_normalized, y_test)
cat("Test Accuracy:", score[[2]])
# Get predicted probabilities
y_pred_prob <- model %>% predict(X_all_test_normalized)
# Convert probabilities to class labels (0 or 1) using a threshold of 0.5
y_pred <- ifelse(y_pred_prob > 0.5, 1, 0)
# Create confusion matrix
conf_matrix <- table(Predicted = y_pred, Actual = y_test)
print(conf_matrix)
library(caret)
# Convert to factor for caret compatibility
y_pred_factor <- factor(y_pred, levels = c(0,1))
y_test_factor <- factor(y_test, levels = c(0,1))
# Generate confusion matrix
confusionMatrix(y_pred_factor, y_test_factor)
library(pROC)
roc_curve <- roc(y_test, y_pred_prob)
# Plot the ROC curve
plot(roc_curve, col = "blue", main = "ROC Curve")
auc(roc_curve)
# Load required libraries
library(tidyverse)
library(caret)
library(randomForest)
library(neuralnet)
library(readxl)
data = read_excel("bp_ai.xlsx")
# Remove arterial_map as specified and keep hypot_map as target
data <- data %>% select(-arterial_map)
data = read_excel("bp_ai.xlsx")
# Remove arterial_map as specified and keep hypot_map as target
data <- data %>% select(-arterial_map)
View(data)
data = read_excel("bp_ai.xlsx")
View(data)
# Comprehensive Regularization Analysis Report
## Blood Pressure AI Model Comparison
bp = read.csv("bp.csv")
library(Amelia)
missmap(bp)
lm_map_arm = lm(MAP..mmHg...Arm. ~ MAP..mmHg...Ankle.,
data = bp)
summary(lm_map_arm)
lm_map_inv = lm(MAP..mmHg...Arterial. ~ MAP..mmHg...Ankle.,
data = bp)
summary(lm_map_inv)
lm_sys_arm = lm(Systolic.BP..mmHg...Arm.~Systolic.BP..mmHg...Ankle.,
data = bp)
summary(lm_sys_arm)
lm_sys_inv = lm(Systolic.BP..mmHg...Arterial.~Systolic.BP..mmHg...Ankle.,
data = bp)
summary(lm_sys_inv)
lm_dia_arm = lm(Diastolic.BP..mmHg...Arm.~ Diastolic.BP..mmHg...Ankle.,
data = bp)
summary(lm_dia_arm)
lm_dia_inv = lm(Diastolic.BP..mmHg...Arterial.~Diastolic.BP..mmHg...Ankle.,
data = bp)
summary(lm_dia_inv)
map_lm = lm(MAP..mmHg...Arterial.~ MAP..mmHg...Ankle. + MAP..mmHg...Arm.,
data = bp)
summary(map_lm)
map_lm_arm = lm(MAP..mmHg...Arterial. ~ MAP..mmHg...Arm.,
data=bp)
summary(map_lm_arm)
map_lm_ank = lm(MAP..mmHg...Arterial. ~ MAP..mmHg...Ankle.,
data = bp)
summary(map_lm_ank)
plot(bp$MAP..mmHg...Ankle.,bp$MAP..mmHg...Arm.)
library(car)
vif(map_lm)
anova(map_lm,map_lm_arm)
anova(map_lm,map_lm_ank)
library(irr)
icc_ank = icc(cbind(bp$MAP..mmHg...Arterial.,
bp$MAP..mmHg...Ankle.),
model = "twoway",type="consistency")
icc_ank
icc_arm = icc(cbind(bp$MAP..mmHg...Arterial.,
bp$MAP..mmHg...Arm.),
model = "twoway",type="consistency")
icc_arm
icc_arm_ank = icc(cbind(bp$MAP..mmHg...Arm.,
bp$MAP..mmHg...Ankle.),
model = "twoway",type="consistency")
icc_arm_ank

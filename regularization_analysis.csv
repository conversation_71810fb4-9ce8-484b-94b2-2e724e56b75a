Model,Regularization,ROC_AUC
Neural Network (Small),"L2=0.01, learning_rate=0.001",0.9349421820010055
XGBoost,"L1=0.1, L2=1.0, max_depth=6, learning_rate=0.1",0.9234791352438412
Random Forest,"max_depth=10, min_samples_split=5, min_samples_leaf=2",0.9209653092006034
Neural Network (Large),"L2=0.1 (stronger), learning_rate=0.001",0.9180492709904474
Support Vector Machine,"C=1.0 (regularization), gamma=scale",0.9175967823026647
Gradient Boosting,"max_depth=5, learning_rate=0.1, subsample=0.8",0.910708898944193
Neural Network (Medium),"L2=0.01, learning_rate=0.001",0.9081950729009552
Decision Tree,"max_depth=10, min_samples_split=10, min_samples_leaf=5",0.9
K-Nearest Neighbors,No explicit regularization (k=5),0.8851181498240321
Logistic Regression,"L2 penalty (Ridge), C=1.0",0.8753142282554047
Logistic Regression (L1),"L1 penalty (Lasso), C=0.1 (stronger)",0.8582202111613876

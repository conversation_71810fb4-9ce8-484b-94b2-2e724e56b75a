# Load necessary libraries
library(caret)
library(readxl)

# Load the data
data <- read_excel("bp_ML.xlsx")

# Check the data structure
str(data)
summary(data)

# Remove rows with missing values (if any)
data <- na.omit(data)

# Define the dependent and independent variables
set.seed(123)
trainIndex <- createDataPartition(data$Arterial, p = 0.8, list = FALSE)
trainData <- data[trainIndex, ]
testData <- data[-trainIndex, ]

# Define the training control
trainControl <- trainControl(method = "cv", number = 10)

# Build a linear regression model
model <- train(Arterial ~ ., data = trainData, method = "lm", trControl = trainControl)

# Summary of the model
print(summary(model))

# Make predictions on the test set
predictions <- predict(model, newdata = testData)

# Evaluate model performance
results <- postResample(predictions, testData$Arterial)
print(results)

# Plot actual vs predicted values
plot(testData$Arterial, predictions, main = "Actual vs Predicted Arterial Values",
     xlab = "Actual Values", ylab = "Predicted Values")
abline(0, 1, col = "red")


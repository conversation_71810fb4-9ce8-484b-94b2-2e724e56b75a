# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import train_test_split, GridSearchCV, StratifiedKFold, RandomizedSearchCV
from sklearn.preprocessing import OneHotEncoder, LabelEncoder, PolynomialFeatures, RobustScaler, MinMaxScaler
from sklearn.compose import ColumnTransformer
from sklearn.feature_selection import RFE
from imblearn.over_sampling import SMOTE
from sklearn.ensemble import VotingClassifier, StackingClassifier
from imblearn.pipeline import Pipeline as ImbPipeline
from sklearn.calibration import CalibratedClassifierCV

# Machine Learning Models
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
import xgboost as xgb
from sklearn.neural_network import MLPClassifier
try:
    from lightgbm import LGBMClassifier
except ImportError:
    LGBMClassifier = None
else:
    pass
try:
    from catboost import CatBoostClassifier
except ImportError:
    CatBoostClassifier = None
else:
    pass

from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_curve, auc, roc_auc_score, f1_score
import shap
from sklearn.ensemble import IsolationForest

print("="*80)
print("COMPREHENSIVE MACHINE LEARNING MODEL COMPARISON (CORRECTED)")
print("Dataset: Blood Pressure AI Analysis")
print("="*80)

# Load the data
data = pd.read_excel("bp_ai.xlsx")
print(f"Dataset shape: {data.shape}")
print(f"Columns: {data.columns.tolist()}")

# Remove arterial_map as specified
data = data.drop('arterial_map', axis=1)

# Data Quality: Handle missing values and outliers (initial pass before split)
numerical_cols_initial = data.select_dtypes(include=['int64', 'float64']).columns.tolist()
categorical_cols_initial = data.select_dtypes(include=['object']).columns.tolist()

for col in numerical_cols_initial:
    data[col] = data[col].fillna(data[col].median())
for col in categorical_cols_initial:
    if col != 'hypot_map': # Avoid filling target if it's categorical and has NaNs
        data[col] = data[col].fillna(data[col].mode()[0])

# Outlier capping (can be done before or after split, doing it before for simplicity here)
for col in numerical_cols_initial:
    lower, upper = data[col].quantile([0.01, 0.99])
    data[col] = data[col].clip(lower, upper)

# Feature Engineering: Polynomial and interaction features
poly = PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)
# Use only numerical columns that are not the target for polynomial features
numerical_features_for_poly = [col for col in numerical_cols_initial if col in data.columns and col != 'hypot_map']
X_poly = poly.fit_transform(data[numerical_features_for_poly])
poly_feature_names = [f"poly_{name}" for name in poly.get_feature_names_out(numerical_features_for_poly)]
X_poly_df = pd.DataFrame(X_poly, columns=poly_feature_names, index=data.index)

# Identify categorical columns for OHE (excluding target)
categorical_cols_for_ohe = [col for col in categorical_cols_initial if col in data.columns and col != 'hypot_map']

# Prepare X by concatenating original non-target features and polynomial features
X_original_features = data.drop(['hypot_map'], axis=1)
X = pd.concat([X_original_features, X_poly_df], axis=1)
y = data['hypot_map']

le = LabelEncoder()
y_encoded = le.fit_transform(y)

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y)
y_train_encoded = le.transform(y_train)
y_test_encoded = le.transform(y_test)

# --- Outlier Removal using IsolationForest (Corrected Placement and Application) ---
print("[Outlier Removal] Removing outliers using IsolationForest from training and test sets...")
# Identify numerical columns present in X_train (original + poly)
numerical_cols_in_X_train = X_train.select_dtypes(include=np.number).columns.tolist()

iso = IsolationForest(contamination=0.02, random_state=42)

# Fit on numerical features of X_train
iso.fit(X_train[numerical_cols_in_X_train])

# Predict outliers on X_train and X_test
outlier_pred_train = iso.predict(X_train[numerical_cols_in_X_train])
outlier_pred_test = iso.predict(X_test[numerical_cols_in_X_train]) # Use same columns as trained on

# Create masks for inliers
mask_train = outlier_pred_train != -1
mask_test = outlier_pred_test != -1

# Apply masks to X_train, y_train, y_train_encoded
X_train = X_train[mask_train]
y_train = y_train[mask_train]
y_train_encoded = y_train_encoded[mask_train]

# Apply masks to X_test, y_test, y_test_encoded
X_test = X_test[mask_test]
y_test = y_test[mask_test]
y_test_encoded = y_test_encoded[mask_test]

print(f"Shape of X_train after outlier removal: {X_train.shape}")
print(f"Shape of X_test after outlier removal: {X_test.shape}")


# Try MinMaxScaler as an alternative scaler
use_minmax = False  # Set to True to use MinMaxScaler
if use_minmax:
    num_transformer = MinMaxScaler()
else:
    num_transformer = RobustScaler()

# Define preprocessor to handle all relevant feature types
# Numerical columns for preprocessor will be all numeric columns in X_train
# (original numeric + polynomial features)
numerical_cols_for_preprocessor = X_train.select_dtypes(include=np.number).columns.tolist()

preprocessor = ColumnTransformer(
    transformers=[
        ('num', num_transformer, numerical_cols_for_preprocessor),
        ('cat', OneHotEncoder(drop='first', handle_unknown='ignore', sparse=False), categorical_cols_for_ohe)
    ], remainder='drop' # Drop any columns not specified (e.g., if some original categoricals were dropped)
)

# Define RFE step to be included in the pipeline
print("[Feature Selection] Using RFE with RandomForest as part of the pipeline...")
rfe_estimator_for_pipeline = RandomForestClassifier(n_estimators=50, random_state=42, n_jobs=1)
rfe_step = RFE(estimator=rfe_estimator_for_pipeline, n_features_to_select=20, step=0.1)

# Hyperparameter Tuning: GridSearchCV for key models
gs_params = {
    'Random Forest': {
        'classifier__n_estimators': [100, 200],
        'classifier__max_depth': [8, 10],
    },
    'XGBoost': {
        'classifier__max_depth': [4, 6],
        'classifier__learning_rate': [0.05, 0.1],
    },
    'Support Vector Machine': {
        'classifier__C': [0.5, 1.0],
        'classifier__gamma': ['scale', 'auto'],
    },
    'LightGBM': {
        'classifier__num_leaves': [31, 50],
        'classifier__learning_rate': [0.05, 0.1],
    },
    'CatBoost': {
        'classifier__depth': [4, 6],
        'classifier__learning_rate': [0.05, 0.1],
    },
    'Neural Network (Small)': {
        'classifier__hidden_layer_sizes': [(10, 5)],
        'classifier__alpha': [0.01],
        'classifier__learning_rate_init': [0.001],
        'classifier__max_iter': [1000],
    },
    'Neural Network (Medium)': {
        'classifier__hidden_layer_sizes': [(50, 25)],
        'classifier__alpha': [0.01],
        'classifier__learning_rate_init': [0.001],
        'classifier__max_iter': [1000],
    },
    'Neural Network (Large)': {
        'classifier__hidden_layer_sizes': [(100, 50, 25)],
        'classifier__alpha': [0.1],
        'classifier__learning_rate_init': [0.001],
        'classifier__max_iter': [1000],
    },
}

models = {
    'Random Forest': {
        'model': RandomForestClassifier(random_state=42),
        'params': gs_params['Random Forest'],
        'needs_encoded': False
    },
    'XGBoost': {
        'model': xgb.XGBClassifier(random_state=42, eval_metric='logloss'),
        'params': gs_params['XGBoost'],
        'needs_encoded': True
    },
    'Support Vector Machine': {
        'model': SVC(probability=True, random_state=42),
        'params': gs_params['Support Vector Machine'],
        'needs_encoded': False
    },
    'Neural Network (Small)': {
        'model': MLPClassifier(max_iter=1000, random_state=42), # Base params, tuned by GS
        'params': gs_params['Neural Network (Small)'],
        'needs_encoded': False
    },
    'Neural Network (Medium)': {
        'model': MLPClassifier(max_iter=1000, random_state=42),
        'params': gs_params['Neural Network (Medium)'],
        'needs_encoded': False
    },
    'Neural Network (Large)': {
        'model': MLPClassifier(max_iter=1000, random_state=42),
        'params': gs_params['Neural Network (Large)'],
        'needs_encoded': False
    },
}
if LGBMClassifier:
    models['LightGBM'] = {
        'model': LGBMClassifier(random_state=42),
        'params': gs_params['LightGBM'],
        'needs_encoded': False
    }
if CatBoostClassifier:
    models['CatBoost'] = {
        'model': CatBoostClassifier(verbose=0, random_state=42, allow_writing_files=False, thread_count=1),
        'params': gs_params['CatBoost'],
        'needs_encoded': False
    }

# Base estimators for Voting and Stacking (using default params, could be tuned instances)
ensemble_base_estimators_instances = {
    'rf': RandomForestClassifier(random_state=42),
    'xgb': xgb.XGBClassifier(random_state=42, eval_metric='logloss'),
    'svc': SVC(probability=True, random_state=42, class_weight='balanced'), # Added class_weight
    'mlp_small': MLPClassifier(hidden_layer_sizes=(10, 5), max_iter=1000, random_state=42, alpha=0.01, learning_rate_init=0.001),
    'mlp_medium': MLPClassifier(hidden_layer_sizes=(50, 25), max_iter=1000, random_state=42, alpha=0.01, learning_rate_init=0.001),
    'mlp_large': MLPClassifier(hidden_layer_sizes=(100, 50, 25), max_iter=1000, random_state=42, alpha=0.1, learning_rate_init=0.001),
}
if LGBMClassifier:
    ensemble_base_estimators_instances['lgbm'] = LGBMClassifier(random_state=42)
if CatBoostClassifier:
    ensemble_base_estimators_instances['catboost'] = CatBoostClassifier(verbose=0, random_state=42, allow_writing_files=False, thread_count=1)

ensemble_estimators_for_voting = list(ensemble_base_estimators_instances.items())

# Use StratifiedKFold for all CV
cv_strategy = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

# Update SVM, MLP to use class_weight='balanced' or ensure SMOTE handles imbalance
models['Support Vector Machine']['model'] = SVC(probability=True, random_state=42, class_weight='balanced')
# MLP does not have class_weight directly in constructor for all versions, SMOTE will handle imbalance.

# Use RandomizedSearchCV for neural networks
for nn_name in ['Neural Network (Small)', 'Neural Network (Medium)', 'Neural Network (Large)']:
    if nn_name in models:
        models[nn_name]['search'] = 'random'

# Add stacking ensemble
print("[Ensemble] Adding StackingClassifier...")
# Base estimators for Stacking: use the model instances from the 'models' dict (before they are wrapped in GridSearchCV)
stacking_base_estimators = []
for name, config in models.items():
    if 'Stacking' not in name: # Avoid self-nesting
         # Create a fresh instance for stacking to avoid state issues
        if name == 'Random Forest': stacking_base_estimators.append((name, RandomForestClassifier(random_state=42)))
        elif name == 'XGBoost': stacking_base_estimators.append((name, xgb.XGBClassifier(random_state=42, eval_metric='logloss')))
        elif name == 'Support Vector Machine': stacking_base_estimators.append((name, SVC(probability=True, random_state=42, class_weight='balanced')))
        elif name == 'LightGBM' and LGBMClassifier: stacking_base_estimators.append((name, LGBMClassifier(random_state=42)))
        elif name == 'CatBoost' and CatBoostClassifier: stacking_base_estimators.append((name, CatBoostClassifier(verbose=0, random_state=42, allow_writing_files=False, thread_count=1)))
        # Add NN models if desired, ensure they are simple instances for stacking
        elif 'Neural Network' in name: stacking_base_estimators.append((name, MLPClassifier(max_iter=1000, random_state=42)))


stacking_clf = StackingClassifier(
    estimators=stacking_base_estimators,
    final_estimator=LogisticRegression(max_iter=1000, class_weight='balanced'),
    cv=cv_strategy,
    n_jobs=1 # Stacking can be complex with parallel jobs
)
models['Stacking Ensemble'] = {
    'model': stacking_clf,
    'params': {}, # Stacking itself is not grid-searched here, but its base estimators are (implicitly if tuned versions were used)
    'needs_encoded': False # Depends on final_estimator and base models
}

# Train models with StratifiedKFold and calibration
results = {}
trained_models = {}
y_probs = {}
conf_matrices = {}
class_reports = {}

for name, model_config in models.items():
    print(f"[CV/Calibration] Training {name}...")
    pipeline_steps = [
        ('preprocessor', preprocessor)
    ]
    # Insert RFE step if it's not a Stacking Ensemble
    if name != 'Stacking Ensemble':
        pipeline_steps.append(('feature_selector', rfe_step))

    pipeline_steps.extend([
        ('smote', SMOTE(random_state=42)),
        ('classifier', model_config['model'])
    ])
    imb_pipeline = ImbPipeline(pipeline_steps)

    is_catboost = 'CatBoost' in name
    search_n_jobs = 1 if is_catboost else -1 # CatBoost can have issues with n_jobs=-1 in GridSearchCV
    if is_catboost and hasattr(model_config['model'], 'set_params'):
        model_config['model'].set_params(thread_count=1)

    current_params = model_config['params']
    if not current_params and name == 'Stacking Ensemble': # No grid search for Stacking itself
        search = imb_pipeline # Use the pipeline directly
    elif model_config.get('search') == 'random':
        search = RandomizedSearchCV(imb_pipeline, current_params, n_iter=5, cv=cv_strategy, scoring='roc_auc', n_jobs=search_n_jobs, random_state=42)
    else:
        search = GridSearchCV(imb_pipeline, current_params, cv=cv_strategy, scoring='roc_auc', n_jobs=search_n_jobs)

    target_y_train = y_train_encoded if model_config['needs_encoded'] else y_train
    search.fit(X_train, target_y_train)

    best_estimator_for_eval = search.best_estimator_ if hasattr(search, 'best_estimator_') else search

    y_pred_encoded = best_estimator_for_eval.predict(X_test)
    y_pred = le.inverse_transform(y_pred_encoded) if model_config['needs_encoded'] else y_pred_encoded
    
    y_prob = best_estimator_for_eval.predict_proba(X_test)[:, 1]

    # Calibrate probabilities
    # For calibration, fit CalibratedClassifierCV on the *best_estimator_* from search, using the full pipeline up to the classifier
    # This is complex if RFE is inside. Simpler: calibrate the final model from search.
    # Note: Calibrating a pipeline that includes SMOTE needs care.
    # SMOTE should ideally only be in the training part of CV for calibration.
    # For simplicity here, we calibrate the already tuned model.
    
    # Create a calibration pipeline *without* SMOTE for fitting CalibratedClassifierCV,
    # as SMOTE should not be applied to the data CalibratedClassifierCV sees during its internal CV.
    # The 'best_estimator_for_eval' already has SMOTE if it was part of the search.
    # This part is tricky. A common approach is to calibrate the final classifier.
    # If best_estimator_for_eval is the full pipeline:
    final_classifier_for_calibration = best_estimator_for_eval.named_steps['classifier']
    calibration_pipeline_for_cv = ImbPipeline(best_estimator_for_eval.steps[:-1]) # All steps except classifier
    
    # Transform training data using the pipeline up to the classifier
    X_train_transformed_for_calib = calibration_pipeline_for_cv.transform(X_train)

    calibrated_clf = CalibratedClassifierCV(final_classifier_for_calibration, method='isotonic', cv=cv_strategy) # or 'sigmoid'
    calibrated_clf.fit(X_train_transformed_for_calib, target_y_train) # Fit on transformed train data
    
    # Transform test data for calibrated prediction
    X_test_transformed_for_calib = calibration_pipeline_for_cv.transform(X_test)
    y_prob_cal = calibrated_clf.predict_proba(X_test_transformed_for_calib)[:, 1]

    accuracy = accuracy_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred, pos_label=le.classes_[1]) # Assuming 'Yes' is the positive class and is at index 1
    roc_auc_val = roc_auc_score(y_test_encoded, y_prob_cal) # Use calibrated probabilities for ROC AUC

    conf_matrix = confusion_matrix(y_test, y_pred, labels=le.classes_)
    class_report = classification_report(y_test, y_pred, output_dict=True, labels=le.classes_, target_names=le.classes_)

    results[name] = {'accuracy': accuracy, 'f1_score': f1, 'roc_auc': roc_auc_val}
    trained_models[name] = best_estimator_for_eval # Store the pipeline from search
    y_probs[name] = y_prob_cal
    conf_matrices[name] = conf_matrix
    class_reports[name] = class_report
    print(f"  ✓ {name}: Accuracy={accuracy:.3f}, F1={f1:.3f}, ROC-AUC={roc_auc_val:.3f}")

# Ensemble VotingClassifier
print("[Ensemble] Training VotingClassifier...")
voting_clf = VotingClassifier(estimators=ensemble_estimators_for_voting, voting='soft', n_jobs=1) # n_jobs=1 for stability

# Voting pipeline needs preprocessor, RFE (if used globally, or handle per estimator), SMOTE
voting_pipeline_steps = [
    ('preprocessor', preprocessor),
    ('feature_selector', rfe_step), # Assuming RFE is beneficial for all base estimators in Voting
    ('smote', SMOTE(random_state=42)),
    ('classifier', voting_clf)
]
voting_pipeline = ImbPipeline(voting_pipeline_steps)

voting_pipeline.fit(X_train, y_train) # Fit with original y_train, SMOTE handles imbalance
y_pred_voting = voting_pipeline.predict(X_test)
y_prob_voting = voting_pipeline.predict_proba(X_test)[:, 1]

accuracy_voting = accuracy_score(y_test, y_pred_voting)
f1_voting = f1_score(y_test, y_pred_voting, pos_label=le.classes_[1])
roc_auc_voting = roc_auc_score(y_test_encoded, y_prob_voting)
conf_matrix_voting = confusion_matrix(y_test, y_pred_voting, labels=le.classes_)
class_report_voting = classification_report(y_test, y_pred_voting, output_dict=True, labels=le.classes_, target_names=le.classes_)

results['Voting Ensemble'] = {'accuracy': accuracy_voting, 'f1_score': f1_voting, 'roc_auc': roc_auc_voting}
trained_models['Voting Ensemble'] = voting_pipeline
y_probs['Voting Ensemble'] = y_prob_voting
conf_matrices['Voting Ensemble'] = conf_matrix_voting
class_reports['Voting Ensemble'] = class_report_voting
print(f"  ✓ Voting Ensemble: Accuracy={accuracy_voting:.3f}, F1={f1_voting:.3f}, ROC-AUC={roc_auc_voting:.3f}")

# Model Performance Summary Table
print("\n5. Model Performance Summary Table:")
print("=" * 80)
summary_data = []
for name_key in results:
    summary_data.append({
        'Model': name_key,
        'Accuracy': results[name_key]['accuracy'],
        'F1-Score': results[name_key]['f1_score'],
        'ROC-AUC': results[name_key]['roc_auc']
    })
summary_df = pd.DataFrame(summary_data)
summary_df = summary_df.sort_values('ROC-AUC', ascending=False)
print(summary_df.round(4).to_string(index=False))

# Best Performing Models
print("\n6. Best Performing Models:")
print("-" * 40)
if not summary_df.empty:
    best_accuracy = summary_df.loc[summary_df['Accuracy'].idxmax()]
    best_roc_auc = summary_df.loc[summary_df['ROC-AUC'].idxmax()]
    best_f1 = summary_df.loc[summary_df['F1-Score'].idxmax()]
    print(f"Best Accuracy:  {best_accuracy['Model']} ({best_accuracy['Accuracy']:.4f})")
    print(f"Best ROC-AUC:   {best_roc_auc['Model']} ({best_roc_auc['ROC-AUC']:.4f})")
    print(f"Best F1-Score:  {best_f1['Model']} ({best_f1['F1-Score']:.4f})")
    summary_df.to_csv('model_performance_summary_corrected.csv', index=False)
    print(f"\nSummary table saved to 'model_performance_summary_corrected.csv'")

    # SHAP for best model
    best_model_name_for_shap = best_roc_auc['Model']
    best_model_pipeline_for_shap = trained_models[best_model_name_for_shap]
    actual_classifier_for_shap = best_model_pipeline_for_shap.named_steps['classifier']
    print(f"[Interpretability] Running SHAP for {best_model_name_for_shap} (Classifier: {type(actual_classifier_for_shap).__name__})...")

    try:
        # Create a pipeline for transforming data for SHAP (preprocessor + feature_selector)
        shap_data_prep_steps = []
        if 'preprocessor' in best_model_pipeline_for_shap.named_steps:
            shap_data_prep_steps.append(('preprocessor', best_model_pipeline_for_shap.named_steps['preprocessor']))
        if 'feature_selector' in best_model_pipeline_for_shap.named_steps:
            shap_data_prep_steps.append(('feature_selector', best_model_pipeline_for_shap.named_steps['feature_selector']))
        
        if not shap_data_prep_steps: # Should not happen if preprocessor is always there
            X_test_transformed_for_shap = X_test.copy() # Fallback, likely problematic
            final_feature_names_for_shap = X_test.columns
        else:
            shap_data_preparation_pipeline = ImbPipeline(shap_data_prep_steps)
            X_test_transformed_for_shap_array = shap_data_preparation_pipeline.transform(X_test)

            # Get feature names
            ct_feature_names = []
            if 'preprocessor' in shap_data_preparation_pipeline.named_steps:
                ct_feature_names = shap_data_preparation_pipeline.named_steps['preprocessor'].get_feature_names_out()
            
            final_feature_names_for_shap = ct_feature_names
            if 'feature_selector' in shap_data_preparation_pipeline.named_steps and hasattr(shap_data_preparation_pipeline.named_steps['feature_selector'], 'support_'):
                rfe_support_mask = shap_data_preparation_pipeline.named_steps['feature_selector'].support_
                final_feature_names_for_shap = np.array(ct_feature_names)[rfe_support_mask]
            
            X_test_transformed_for_shap = pd.DataFrame(X_test_transformed_for_shap_array, columns=final_feature_names_for_shap)

        # For some models (like Stacking or Voting), SHAP might need KernelExplainer or specific handling
        if isinstance(actual_classifier_for_shap, (StackingClassifier, VotingClassifier)):
            print(f"SHAP for {type(actual_classifier_for_shap).__name__} can be complex. Using KernelExplainer as a fallback.")
            # KernelExplainer needs a function that takes numpy array and returns model probabilities
            def model_predict_proba_for_kernel(data_asarray):
                data_asframe = pd.DataFrame(data_asarray, columns=X_test_transformed_for_shap.columns)
                return actual_classifier_for_shap.predict_proba(data_asframe)

            # Use a sample of the transformed data for the background distribution
            background_data_for_kernel = shap.sample(X_test_transformed_for_shap, 100) # Sample 100 instances
            explainer = shap.KernelExplainer(model_predict_proba_for_kernel, background_data_for_kernel)
            shap_values = explainer.shap_values(X_test_transformed_for_shap) # Will get shap values for each class
            # For binary classification, plot for the positive class (e.g., class 1)
            shap.summary_plot(shap_values[1], X_test_transformed_for_shap, show=False)

        else:
            explainer = shap.Explainer(actual_classifier_for_shap, X_test_transformed_for_shap)
            shap_values = explainer(X_test_transformed_for_shap)
            shap.summary_plot(shap_values, X_test_transformed_for_shap, show=False)

        plt.savefig('shap_summary_corrected.png', dpi=300, bbox_inches='tight')
        print("SHAP summary plot saved as 'shap_summary_corrected.png'")
    except Exception as e:
        print(f"SHAP analysis failed for {best_model_name_for_shap}: {e}. This can happen if the model or data format is not directly compatible. Consider specific SHAP explainers for complex models.")

else:
    print("No models were successfully trained, skipping result summary and SHAP.")

# ROC curves
plt.figure(figsize=(12, 8))
colors = plt.cm.tab10(np.linspace(0, 1, len(y_probs)))
for i, (name, y_prob_plot) in enumerate(y_probs.items()):
    try:
        fpr, tpr, _ = roc_curve(y_test_encoded, y_prob_plot)
        roc_auc_val_plot = auc(fpr, tpr)
        plt.plot(fpr, tpr, color=colors[i], linewidth=2,
                label=f'{name} (AUC = {roc_auc_val_plot:.3f})')
    except Exception as e:
        print(f"Could not plot ROC for {name}: {e}")
        continue
plt.plot([0, 1], [0, 1], 'k--', linewidth=1, alpha=0.8)
plt.xlabel('False Positive Rate', fontsize=12)
plt.ylabel('True Positive Rate', fontsize=12)
plt.title('ROC Curves for Classification Models (Corrected)', fontsize=14, fontweight='bold')
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig('roc_curves_corrected.png', dpi=300, bbox_inches='tight')
plt.show()

# Confusion matrices
n_models_plot = len(conf_matrices)
if n_models_plot > 0:
    n_cols_plot = 3
    n_rows_plot = (n_models_plot + n_cols_plot - 1) // n_cols_plot
    fig, axes = plt.subplots(n_rows_plot, n_cols_plot, figsize=(15, 5*n_rows_plot))
    if n_models_plot == 1: # Handle single model case for axes
        axes = np.array([axes]).flatten()
    else:
        axes = axes.flatten()

    idx = 0
    for name_cm, conf_matrix_cm in conf_matrices.items():
        if idx < len(axes):
            sns.heatmap(conf_matrix_cm, annot=True, fmt='d', cmap='Blues',
                        xticklabels=le.classes_, yticklabels=le.classes_, ax=axes[idx])
            axes[idx].set_title(f'{name_cm}', fontweight='bold')
            axes[idx].set_xlabel('Predicted')
            axes[idx].set_ylabel('Actual')
            idx +=1
    
    for i in range(idx, len(axes)): # Hide unused subplots
        axes[i].set_visible(False)

    plt.tight_layout()
    plt.savefig('confusion_matrices_corrected.png', dpi=300, bbox_inches='tight')
    plt.show()

# Error analysis: show misclassified samples for best model
if not summary_df.empty and best_roc_auc['Model'] in trained_models:
    print("\n[Error Analysis] Showing misclassified samples for best ROC-AUC model...")
    best_model_for_error_analysis_name = best_roc_auc['Model']
    best_model_for_error_analysis = trained_models[best_model_for_error_analysis_name]
    
    y_pred_best_model_error = best_model_for_error_analysis.predict(X_test)
    # If predictions are encoded, decode them for comparison with original y_test
    if models[best_model_for_error_analysis_name]['needs_encoded']:
         y_pred_best_model_error = le.inverse_transform(y_pred_best_model_error)

    misclassified_indices = X_test.index[y_pred_best_model_error != y_test]
    misclassified_samples = X_test.loc[misclassified_indices]
    
    print(f"Number of misclassified samples by {best_model_for_error_analysis_name}: {misclassified_samples.shape[0]}")
    if not misclassified_samples.empty:
        print("Example misclassified samples (first 5):")
        print(misclassified_samples.head())
else:
    print("Skipping error analysis as no best model identified or trained.")

print("\nAnalysis complete! Check the generated files for detailed results (e.g., *_corrected.png, *_corrected.csv).")
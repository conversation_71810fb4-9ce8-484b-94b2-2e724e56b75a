# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import train_test_split, GridSearchCV, StratifiedKFold, RandomizedSearchCV
from sklearn.preprocessing import OneHotEncoder, LabelEncoder, PolynomialFeatures, RobustScaler, MinMaxScaler
from sklearn.compose import ColumnTransformer
from sklearn.feature_selection import SelectKBest, f_classif, RFE
from imblearn.over_sampling import SMOTE
from sklearn.ensemble import VotingClassifier, StackingClassifier
from imblearn.pipeline import Pipeline as ImbPipeline
from sklearn.calibration import CalibratedClassifierCV

# Machine Learning Models
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
import xgboost as xgb
from sklearn.neural_network import MLPClassifier
try:
    from lightgbm import LGBMClassifier
except ImportError:
    LGBMClassifier = None
try:
    from catboost import CatBoostClassifier
except ImportError:
    CatBoostClassifier = None

from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_curve, auc, roc_auc_score, f1_score
import shap
from sklearn.ensemble import IsolationForest

print("="*80)
print("COMPREHENSIVE MACHINE LEARNING MODEL COMPARISON (IMPROVED)")
print("Dataset: Blood Pressure AI Analysis")
print("="*80)

# Load the data
data = pd.read_excel("bp_ai.xlsx")
print(f"Dataset shape: {data.shape}")
print(f"Columns: {data.columns.tolist()}")

# Remove arterial_map as specified
data = data.drop('arterial_map', axis=1)

# Data Quality: Handle missing values and outliers
numerical_cols = data.select_dtypes(include=['int64', 'float64']).columns.tolist()
categorical_cols = data.select_dtypes(include=['object']).columns.tolist()
for col in numerical_cols:
    data[col] = data[col].fillna(data[col].median())
for col in categorical_cols:
    data[col] = data[col].fillna(data[col].mode()[0])
for col in numerical_cols:
    lower, upper = data[col].quantile([0.01, 0.99])
    data[col] = data[col].clip(lower, upper)

# Feature Engineering: Polynomial and interaction features
poly = PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)
X_poly = poly.fit_transform(data[numerical_cols])
poly_feature_names = [f"poly_{name}" for name in poly.get_feature_names_out(numerical_cols)]
X_poly_df = pd.DataFrame(X_poly, columns=poly_feature_names, index=data.index)
categorical_cols = [col for col in categorical_cols if col in data.columns and col != 'hypot_map']
X = pd.concat([data.drop(['hypot_map'], axis=1), X_poly_df], axis=1)
y = data['hypot_map']

le = LabelEncoder()
y_encoded = le.fit_transform(y)

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y)
y_train_encoded = le.transform(y_train)
y_test_encoded = le.transform(y_test)

# Outlier removal using IsolationForest
print("[Outlier Removal] Removing outliers using IsolationForest...")
iso = IsolationForest(contamination=0.02, random_state=42)
outlier_pred = iso.fit_predict(X[numerical_cols])
mask = outlier_pred != -1
X = X[mask]
y = y[mask]

# Try MinMaxScaler as an alternative scaler
use_minmax = False  # Set to True to use MinMaxScaler
if use_minmax:
    scaler = MinMaxScaler()
    num_transformer = scaler
else:
    num_transformer = RobustScaler()

# Feature selection with RFE (Recursive Feature Elimination)
print("[Feature Selection] Running RFE with RandomForest...")
# Only use numeric columns (including polynomial features) for RFE
numeric_for_rfe = [col for col in X.columns if col not in categorical_cols]
rfe_selector = RFE(RandomForestClassifier(n_estimators=50, random_state=42), n_features_to_select=20)
rfe_selector.fit(X[numeric_for_rfe], y)
selected_rfe_numeric = X[numeric_for_rfe].columns[rfe_selector.support_].tolist()
# Combine selected numeric features with all categorical features
selected_rfe_features = selected_rfe_numeric + categorical_cols

# Update preprocessor to use selected RFE features
preprocessor = ColumnTransformer(
    transformers=[
        ('num', num_transformer, [f for f in selected_rfe_features if f in poly_feature_names or f in numerical_cols]),
        ('cat', OneHotEncoder(drop='first', handle_unknown='ignore'), [f for f in selected_rfe_features if f in categorical_cols])
    ])

# Hyperparameter Tuning: GridSearchCV for key models
gs_params = {
    'Random Forest': {
        'classifier__n_estimators': [100, 200],
        'classifier__max_depth': [8, 10],
    },
    'XGBoost': {
        'classifier__max_depth': [4, 6],
        'classifier__learning_rate': [0.05, 0.1],
    },
    'Support Vector Machine': {
        'classifier__C': [0.5, 1.0],
        'classifier__gamma': ['scale', 'auto'],
    },
    'LightGBM': {
        'classifier__num_leaves': [31, 50],
        'classifier__learning_rate': [0.05, 0.1],
    },
    'CatBoost': {
        'classifier__depth': [4, 6],
        'classifier__learning_rate': [0.05, 0.1],
    },
    'Neural Network (Small)': {
        'classifier__hidden_layer_sizes': [(10, 5)],
        'classifier__alpha': [0.01],
        'classifier__learning_rate_init': [0.001],
        'classifier__max_iter': [1000],
    },
    'Neural Network (Medium)': {
        'classifier__hidden_layer_sizes': [(50, 25)],
        'classifier__alpha': [0.01],
        'classifier__learning_rate_init': [0.001],
        'classifier__max_iter': [1000],
    },
    'Neural Network (Large)': {
        'classifier__hidden_layer_sizes': [(100, 50, 25)],
        'classifier__alpha': [0.1],
        'classifier__learning_rate_init': [0.001],
        'classifier__max_iter': [1000],
    },
}

models = {
    'Random Forest': {
        'model': RandomForestClassifier(random_state=42),
        'params': gs_params['Random Forest'],
        'needs_encoded': False
    },
    'XGBoost': {
        'model': xgb.XGBClassifier(random_state=42, eval_metric='logloss'),
        'params': gs_params['XGBoost'],
        'needs_encoded': True
    },
    'Support Vector Machine': {
        'model': SVC(probability=True, random_state=42),
        'params': gs_params['Support Vector Machine'],
        'needs_encoded': False
    },
    'Neural Network (Small)': {
        'model': MLPClassifier(hidden_layer_sizes=(10, 5), max_iter=1000, random_state=42, alpha=0.01, learning_rate_init=0.001),
        'params': gs_params['Neural Network (Small)'],
        'needs_encoded': False
    },
    'Neural Network (Medium)': {
        'model': MLPClassifier(hidden_layer_sizes=(50, 25), max_iter=1000, random_state=42, alpha=0.01, learning_rate_init=0.001),
        'params': gs_params['Neural Network (Medium)'],
        'needs_encoded': False
    },
    'Neural Network (Large)': {
        'model': MLPClassifier(hidden_layer_sizes=(100, 50, 25), max_iter=1000, random_state=42, alpha=0.1, learning_rate_init=0.001),
        'params': gs_params['Neural Network (Large)'],
        'needs_encoded': False
    },
}
if LGBMClassifier:
    models['LightGBM'] = {
        'model': LGBMClassifier(random_state=42),
        'params': gs_params['LightGBM'],
        'needs_encoded': False
    }
if CatBoostClassifier:
    models['CatBoost'] = {
        'model': CatBoostClassifier(verbose=0, random_state=42, allow_writing_files=False, thread_count=1),
        'params': gs_params['CatBoost'],
        'needs_encoded': False
    }
ensemble_estimators = [
    ('rf', RandomForestClassifier(random_state=42)),
    ('xgb', xgb.XGBClassifier(random_state=42, eval_metric='logloss')),
    ('svc', SVC(probability=True, random_state=42)),
    ('mlp_small', MLPClassifier(hidden_layer_sizes=(10, 5), max_iter=1000, random_state=42, alpha=0.01, learning_rate_init=0.001)),
    ('mlp_medium', MLPClassifier(hidden_layer_sizes=(50, 25), max_iter=1000, random_state=42, alpha=0.01, learning_rate_init=0.001)),
    ('mlp_large', MLPClassifier(hidden_layer_sizes=(100, 50, 25), max_iter=1000, random_state=42, alpha=0.1, learning_rate_init=0.001)),
]
if LGBMClassifier:
    ensemble_estimators.append(('lgbm', LGBMClassifier(random_state=42)))
if CatBoostClassifier:
    ensemble_estimators.append(('catboost', CatBoostClassifier(verbose=0, random_state=42, allow_writing_files=False, thread_count=1)))

# Use StratifiedKFold for all CV
cv_strategy = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

# Update SVM, MLP, and LogisticRegression to use class_weight='balanced'
models['Support Vector Machine']['model'] = SVC(probability=True, random_state=42, class_weight='balanced')
models['Neural Network (Small)']['model'] = MLPClassifier(hidden_layer_sizes=(10, 5), max_iter=1000, random_state=42, alpha=0.01, learning_rate_init=0.001)
models['Neural Network (Medium)']['model'] = MLPClassifier(hidden_layer_sizes=(50, 25), max_iter=1000, random_state=42, alpha=0.01, learning_rate_init=0.001)
models['Neural Network (Large)']['model'] = MLPClassifier(hidden_layer_sizes=(100, 50, 25), max_iter=1000, random_state=42, alpha=0.1, learning_rate_init=0.001)

# Use RandomizedSearchCV for neural networks
for nn in ['Neural Network (Small)', 'Neural Network (Medium)', 'Neural Network (Large)']:
    models[nn]['search'] = 'random'

# Add stacking ensemble
print("[Ensemble] Adding StackingClassifier...")
base_estimators = [(k, v['model']) for k, v in models.items() if 'Neural Network' not in k]
stacking = StackingClassifier(estimators=base_estimators, final_estimator=LogisticRegression(max_iter=1000), cv=cv_strategy, n_jobs=1)
models['Stacking Ensemble'] = {
    'model': stacking,
    'params': {},
    'needs_encoded': False
}
ensemble_estimators.append(('stacking', stacking))

# Train models with StratifiedKFold and calibration
results = {}
trained_models = {}
y_probs = {}
conf_matrices = {}
class_reports = {}
for name, model_config in models.items():
    print(f"[CV/Calibration] Training {name}...")
    imb_pipeline = ImbPipeline([
        ('preprocessor', preprocessor),
        ('smote', SMOTE(random_state=42)),
        ('classifier', model_config['model'])
    ])
    # Special handling for CatBoost: set n_jobs=1 in search and thread_count=1 in the model
    is_catboost = 'CatBoost' in name
    search_n_jobs = 1 if is_catboost else -1
    if is_catboost:
        # Set thread_count=1 for CatBoost model if not already set
        if hasattr(model_config['model'], 'set_params'):
            model_config['model'].set_params(thread_count=1)
    if model_config.get('search') == 'random':
        search = RandomizedSearchCV(imb_pipeline, model_config['params'], n_iter=5, cv=cv_strategy, scoring='roc_auc', n_jobs=search_n_jobs, random_state=42)
    else:
        search = GridSearchCV(imb_pipeline, model_config['params'], cv=cv_strategy, scoring='roc_auc', n_jobs=search_n_jobs)
    # Use encoded y for models that need it (like XGBoost)
    if model_config['needs_encoded']:
        search.fit(X_train, y_train_encoded)
        y_pred = le.inverse_transform(search.predict(X_test))
        y_prob = search.predict_proba(X_test)[:, 1]
        # Calibrate with encoded y
        calibrated = CalibratedClassifierCV(search.best_estimator_, cv=cv_strategy)
        calibrated.fit(X_train, y_train_encoded)
        y_prob_cal = calibrated.predict_proba(X_test)[:, 1]
        roc_auc = roc_auc_score(y_test_encoded, y_prob_cal)
    else:
        search.fit(X_train, y_train)
        y_pred = search.predict(X_test)
        y_prob = search.predict_proba(X_test)[:, 1]
        # Calibrate with original y
        calibrated = CalibratedClassifierCV(search.best_estimator_, cv=cv_strategy)
        calibrated.fit(X_train, y_train)
        y_prob_cal = calibrated.predict_proba(X_test)[:, 1]
        roc_auc = roc_auc_score(y_test_encoded, y_prob_cal)
    accuracy = accuracy_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred, pos_label='Yes')
    conf_matrix = confusion_matrix(y_test, y_pred)
    class_report = classification_report(y_test, y_pred, output_dict=True)
    results[name] = {'accuracy': accuracy, 'f1_score': f1, 'roc_auc': roc_auc}
    trained_models[name] = search.best_estimator_
    y_probs[name] = y_prob_cal
    conf_matrices[name] = conf_matrix
    class_reports[name] = class_report
    print(f"  ✓ {name}: Accuracy={accuracy:.3f}, F1={f1:.3f}, ROC-AUC={roc_auc:.3f}")

# Ensemble VotingClassifier
print("[Ensemble] Training VotingClassifier...")
voting = VotingClassifier(ensemble_estimators, voting='soft', n_jobs=1)
voting_pipeline = ImbPipeline([
    ('preprocessor', preprocessor),
    ('smote', SMOTE(random_state=42)),
    ('classifier', voting)
])
voting_pipeline.fit(X_train, y_train)
y_pred = voting_pipeline.predict(X_test)
y_prob = voting_pipeline.predict_proba(X_test)[:, 1]
accuracy = accuracy_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred, pos_label='Yes')
roc_auc = roc_auc_score(y_test_encoded, y_prob)
conf_matrix = confusion_matrix(y_test, y_pred)
class_report = classification_report(y_test, y_pred, output_dict=True)
results['Voting Ensemble'] = {'accuracy': accuracy, 'f1_score': f1, 'roc_auc': roc_auc}
trained_models['Voting Ensemble'] = voting_pipeline
y_probs['Voting Ensemble'] = y_prob
conf_matrices['Voting Ensemble'] = conf_matrix
class_reports['Voting Ensemble'] = class_report
print(f"  ✓ Voting Ensemble: Accuracy={accuracy:.3f}, F1={f1:.3f}, ROC-AUC={roc_auc:.3f}")

# Model Performance Summary Table
print("\n5. Model Performance Summary Table:")
print("=" * 80)
summary_data = []
for name in results:
    summary_data.append({
        'Model': name,
        'Accuracy': results[name]['accuracy'],
        'F1-Score': results[name]['f1_score'],
        'ROC-AUC': results[name]['roc_auc']
    })
summary_df = pd.DataFrame(summary_data)
summary_df = summary_df.sort_values('ROC-AUC', ascending=False)
print(summary_df.round(4).to_string(index=False))

# Best Performing Models
print("\n6. Best Performing Models:")
print("-" * 40)
best_accuracy = summary_df.loc[summary_df['Accuracy'].idxmax()]
best_roc_auc = summary_df.loc[summary_df['ROC-AUC'].idxmax()]
best_f1 = summary_df.loc[summary_df['F1-Score'].idxmax()]
print(f"Best Accuracy:  {best_accuracy['Model']} ({best_accuracy['Accuracy']:.4f})")
print(f"Best ROC-AUC:   {best_roc_auc['Model']} ({best_roc_auc['ROC-AUC']:.4f})")
print(f"Best F1-Score:  {best_f1['Model']} ({best_f1['F1-Score']:.4f})")

summary_df.to_csv('model_performance_summary.csv', index=False)
print(f"\nSummary table saved to 'model_performance_summary.csv'")

# SHAP for best model
best_model_name = max(results, key=lambda k: results[k]['roc_auc'])
best_model = trained_models[best_model_name]
print(f"[Interpretability] Running SHAP for {best_model_name}...")
try:
    explainer = shap.Explainer(best_model.named_steps['classifier'], X_test)
    shap_values = explainer(X_test)
    shap.summary_plot(shap_values, X_test, feature_names=X_test.columns, show=False)
    plt.savefig('shap_summary.png', dpi=300, bbox_inches='tight')
    print("SHAP summary plot saved as 'shap_summary.png'")
except Exception as e:
    print(f"SHAP analysis failed: {e}")

# ROC curves
plt.figure(figsize=(12, 8))
colors = plt.cm.tab10(np.linspace(0, 1, len(y_probs)))
for i, (name, y_prob) in enumerate(y_probs.items()):
    try:
        fpr, tpr, _ = roc_curve(y_test_encoded, y_prob)
        roc_auc = auc(fpr, tpr)
        plt.plot(fpr, tpr, color=colors[i], linewidth=2,
                label=f'{name} (AUC = {roc_auc:.3f})')
    except:
        continue
plt.plot([0, 1], [0, 1], 'k--', linewidth=1, alpha=0.8)
plt.xlabel('False Positive Rate', fontsize=12)
plt.ylabel('True Positive Rate', fontsize=12)
plt.title('ROC Curves for Classification Models', fontsize=14, fontweight='bold')
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig('roc_curves.png', dpi=300, bbox_inches='tight')
plt.show()

# Confusion matrices
n_models = len(results)
n_cols = 3
n_rows = (n_models + n_cols - 1) // n_cols
fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))
if n_rows == 1:
    axes = axes.reshape(1, -1)
axes = axes.flatten()
for i, (name, conf_matrix) in enumerate(conf_matrices.items()):
    sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues',
                xticklabels=['No', 'Yes'], yticklabels=['No', 'Yes'], ax=axes[i])
    axes[i].set_title(f'{name}', fontweight='bold')
    axes[i].set_xlabel('Predicted')
    axes[i].set_ylabel('Actual')
for i in range(len(results), len(axes)):
    axes[i].set_visible(False)
plt.tight_layout()
plt.savefig('confusion_matrices.png', dpi=300, bbox_inches='tight')
plt.show()

# Error analysis: show misclassified samples
print("\n[Error Analysis] Showing misclassified samples for best model...")
best_model_name = max(results, key=lambda k: results[k]['roc_auc'])
best_model = trained_models[best_model_name]
y_pred_best = best_model.predict(X_test)
misclassified = X_test[y_pred_best != y_test]
print(f"Number of misclassified samples: {misclassified.shape[0]}")
print(misclassified.head())

# (Optional) Add AutoML/Optuna integration here for further automation

print("\nAnalysis complete! Check the generated files for detailed results.")

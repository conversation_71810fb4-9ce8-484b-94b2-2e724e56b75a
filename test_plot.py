import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import os

print(f"Current working directory: {os.getcwd()}")
print(f"Directory contents before: {os.listdir('.')}")

# Create a simple plot
plt.figure()
plt.plot([1, 2, 3], [1, 2, 3])
plt.title('Test Plot')

# Try to save with full path
save_path = os.path.join(os.getcwd(), 'test_plot.png')
print(f"Attempting to save to: {save_path}")
plt.savefig(save_path)
plt.close()

print(f"Directory contents after: {os.listdir('.')}")
if os.path.exists('test_plot.png'):
    print("Test plot was successfully saved!")
else:
    print("Failed to save test plot!")

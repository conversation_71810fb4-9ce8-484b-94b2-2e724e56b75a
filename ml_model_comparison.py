import pandas as pd
import numpy as np
import sys
import os
import matplotlib
matplotlib.use('Agg')
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_curve, auc
import matplotlib.pyplot as plt
import seaborn as sns

# Set random seed for reproducibility
np.random.seed(42)

def verify_predictions(y_pred, y_pred_proba):
    """Verify prediction outputs"""
    print(f"Prediction shape: {y_pred.shape}")
    print(f"Prediction probabilities shape: {y_pred_proba.shape}")
    print(f"Unique predicted classes: {np.unique(y_pred)}")
    print(f"Probability range: [{y_pred_proba.min():.3f}, {y_pred_proba.max():.3f}]")

def verify_metrics(accuracy, conf_matrix, roc_auc):
    """Verify metric calculations"""
    print(f"Accuracy: {accuracy:.4f}")
    print(f"ROC AUC: {roc_auc:.4f}")
    print("Confusion Matrix:")
    print(conf_matrix)
    # Basic sanity checks
    assert 0 <= accuracy <= 1, "Accuracy should be between 0 and 1"
    assert 0 <= roc_auc <= 1, "ROC AUC should be between 0 and 1"

# Load and preprocess data
try:
    print("Loading data...")
    data = pd.read_excel('bp_ai.xlsx')
    print(f"Data loaded successfully. Shape: {data.shape}")
    print("\nColumns:", data.columns.tolist())
    print("\nSample of data:")
    print(data.head())
except Exception as e:
    print(f"Error loading data: {str(e)}")
    sys.exit(1)

# Data preprocessing
print("\nPreprocessing data...")

# Encode categorical variables
categorical_vars = ['gender', 'asa', 'hypothyro', 'dm']
label_encoder = LabelEncoder()
for var in categorical_vars:
    data[var] = label_encoder.fit_transform(data[var])
    print(f"Encoded {var} unique values: {np.unique(data[var])}")

# Encode target variable
data['hypot_map'] = label_encoder.fit_transform(data['hypot_map'])
print(f"Target variable unique values: {np.unique(data['hypot_map'])}")

# Remove arterial_map as specified
X = data.drop(['hypot_map', 'arterial_map'], axis=1)
y = data['hypot_map']

print("\nFeature names:", X.columns.tolist())
print(f"Number of features: {X.shape[1]}")
print(f"Number of samples: {X.shape[0]}")

# Split the data
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
print(f"\nTraining set shape: {X_train.shape}")
print(f"Testing set shape: {X_test.shape}")

# Scale the features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Initialize models
models = {
    'Logistic Regression': LogisticRegression(random_state=42),
    'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
    'SVM': SVC(probability=True, random_state=42),
    'Neural Network': MLPClassifier(hidden_layer_sizes=(100, 50), max_iter=1000, random_state=42)
}

# Dictionary to store results
results = {}

# Train and evaluate models
print("\nTraining and evaluating models...")
plt.figure(figsize=(15, 10))

for idx, (name, model) in enumerate(models.items(), 1):
    print(f"\n{'='*50}")
    print(f"Training and testing {name}...")
    
    # Train model
    try:
        model.fit(X_train_scaled, y_train)
        print(f"{name} trained successfully")
    except Exception as e:
        print(f"Error training {name}: {str(e)}")
        continue
    
    # Make predictions
    try:
        y_pred = model.predict(X_test_scaled)
        y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]
        print(f"\nVerifying predictions for {name}:")
        verify_predictions(y_pred, y_pred_proba)
    except Exception as e:
        print(f"Error making predictions with {name}: {str(e)}")
        continue
    
    # Calculate metrics
    try:
        accuracy = accuracy_score(y_test, y_pred)
        class_report = classification_report(y_test, y_pred)
        conf_matrix = confusion_matrix(y_test, y_pred)
        fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
        roc_auc = auc(fpr, tpr)
        
        print(f"\nVerifying metrics for {name}:")
        verify_metrics(accuracy, conf_matrix, roc_auc)
        print("\nClassification Report:")
        print(class_report)
    except Exception as e:
        print(f"Error calculating metrics for {name}: {str(e)}")
        continue
    
    # Store results
    results[name] = {
        'accuracy': accuracy,
        'classification_report': class_report,
        'confusion_matrix': conf_matrix,
        'roc_auc': roc_auc,
        'fpr': fpr,
        'tpr': tpr
    }
    
    # Plot ROC curve
    try:
        plt.subplot(2, 2, idx)
        plt.plot(fpr, tpr, label=f'ROC curve (AUC = {roc_auc:.2f})')
        plt.plot([0, 1], [0, 1], 'k--')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title(f'{name} ROC Curve')
        plt.legend(loc="lower right")
    except Exception as e:
        print(f"Error plotting ROC curve for {name}: {str(e)}")

# Save ROC curves
plt.tight_layout()
try:
    save_path = os.path.join(os.getcwd(), 'roc_curves.png')
    plt.savefig(save_path)
    print(f"\nSaved ROC curves to: {save_path}")
except Exception as e:
    print(f"Error saving ROC curves: {str(e)}")
plt.close()

# Print final results and save confusion matrices
print("\nFinal Model Performance Summary:")
print("=" * 50)

for name, result in results.items():
    print(f"\n{name}:")
    print(f"Accuracy: {result['accuracy']:.4f}")
    print(f"ROC AUC: {result['roc_auc']:.4f}")
    print("\nClassification Report:")
    print(result['classification_report'])
    
    # Plot confusion matrix
    try:
        plt.figure(figsize=(8, 6))
        sns.heatmap(result['confusion_matrix'], annot=True, fmt='d', cmap='Blues')
        plt.title(f'{name} Confusion Matrix')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        save_path = os.path.join(os.getcwd(), f'{name.lower().replace(" ", "_")}_confusion_matrix.png')
        plt.savefig(save_path)
        print(f"Saved confusion matrix to: {save_path}")
        plt.close()
    except Exception as e:
        print(f"Error saving confusion matrix for {name}: {str(e)}")

# Feature importance for Random Forest
try:
    rf_model = models['Random Forest']
    feature_importance = pd.DataFrame({
        'feature': X.columns,
        'importance': rf_model.feature_importances_
    }).sort_values('importance', ascending=False)

    plt.figure(figsize=(10, 6))
    sns.barplot(x='importance', y='feature', data=feature_importance)
    plt.title('Feature Importance (Random Forest)')
    save_path = os.path.join(os.getcwd(), 'feature_importance.png')
    plt.savefig(save_path)
    print(f"\nSaved feature importance plot to: {save_path}")
    print("\nTop 5 most important features:")
    print(feature_importance.head())
    plt.close()
except Exception as e:
    print(f"Error creating feature importance plot: {str(e)}")

print("\nAnalysis complete! Visualization files have been saved:")
print("- roc_curves.png")
print("- feature_importance.png")
print("- [model_name]_confusion_matrix.png for each model")

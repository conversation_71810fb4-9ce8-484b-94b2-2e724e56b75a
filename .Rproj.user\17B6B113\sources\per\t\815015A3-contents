# Load necessary libraries
library(caret)
library(readxl)

# Load the data

data <- read_excel("bp_ML.xlsx")

# Check the data structure
str(data)
summary(data)

# Remove rows with missing values (if any)
data <- na.omit(data)

# Define the dependent and independent variables
set.seed(123)
trainIndex <- createDataPartition(data$Arterial, p = 0.8, list = FALSE)
trainData <- data[trainIndex, ]
testData <- data[-trainIndex, ]

# Define the training control
trainControl <- trainControl(method = "cv", number = 10)

# Build a random forest model
model <- train(Arterial ~ ., data = trainData, 
               method = "rf", trControl = trainControl)

# Summary of the model
print(model)

# Make predictions on the test set
predictions <- predict(model, newdata = testData)

# Evaluate model performance
results <- postResample(predictions, testData$Arterial)
print(results)

# Plot actual vs predicted values
plot(testData$Arterial, predictions, 
     main = "Actual vs Predicted Arterial Values",
     xlab = "Actual Values", ylab = "Predicted Values")
abline(0, 1, col = "red")




# Load necessary libraries
library(caret)
library(readxl)

# Load the data

data <- read_excel("bp_ai.xlsx")

data$gender <- ifelse(data$gender == "Male", 0, 1)
data$asa <- ifelse(data$asa == "I", 1, 2)
data$hypothyroid <- ifelse(data$hypothyroid == "Yes", 1, 0)
data$dm <- ifelse(data$dm == "Yes", 1, 0)
data$hypot_map = ifelse(data$hypot_map == "Yes", 1, 0)

X_all <- data[,-c(which(names(bp) == "arterial_map"),
                  which(names(bp) == "hypot_map"))]
y = data$arterial_map

# Split into training (80%) and testing (20%) sets
set.seed(103)
train_idx <- sample(1:nrow(bp), 0.8 * nrow(bp))
X_all_train <- X_all[train_idx, ]
X_all_test <- X_all[-train_idx, ]
y_train <- y[train_idx]
y_test <- y[-train_idx]

# Normalize continuous variables only
X_continuous_mean <- apply(X_all_train[, c(1,2,7,8,9,10)], 2, mean)
X_continuous_sd <- apply(X_all_train[, c(1,2,7,8,9,10)], 2, sd)
X_all_train_normalized <- X_all_train
X_all_test_normalized <- X_all_test
X_all_train_normalized[, c(1,2,7,8,9,10)] <- scale(X_all_train[, c(1,2,7,8,9,10)], 
                                                   center = X_continuous_mean, scale = X_continuous_sd)
X_all_test_normalized[, c(1,2,7,8,9,10)] <- scale(X_all_test[, c(1,2,7,8,9,10)], 
                                                  center = X_continuous_mean, scale = X_continuous_sd)

ml_data = data.frame(X_all_train_normalized, y_train)

# Define the training control
trainControl <- trainControl(method = "cv", number = 10)

# Build a random forest regression model
model <- train(y_train ~ ., data = ml_data, 
               method = "rf", trControl = trainControl)

# Summary of the model
print(model)

# Make predictions on the test set
predictions <- predict(model, newdata = X_all_test_normalized)

# Evaluate model performance
results <- postResample(predictions, y_test)
print(results)

# Plot actual vs predicted values
plot(y_test, predictions, 
     main = "Actual vs Predicted Arterial Values",
     xlab = "Actual Values", ylab = "Predicted Values")
abline(0, 1, col = "red")


#Classification model
y = as.factor(data$hypot_map)
y_train <- y[train_idx]
y_test <- y[-train_idx]
ml_data = data.frame(X_all_train_normalized, y_train)
model <- train(y_train ~ ., data = ml_data, 
               method = "rf", trControl = trainControl)
print(model)


# Predict on test data
predictions <- predict(model, newdata = X_all_test_normalized)

# View first few predictions
head(predictions)

# Generate confusion matrix
conf_matrix <- confusionMatrix(predictions, y_test)
print(conf_matrix)

# Plot feature importance
library(randomForest)
varImpPlot(model$finalModel)

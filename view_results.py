#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to view the generated machine learning analysis results
"""

import pandas as pd
import matplotlib.pyplot as plt
from PIL import Image
import os

def display_summary():
    """Display the model performance summary"""
    print("="*80)
    print("MACHINE LEARNING MODEL COMPARISON RESULTS")
    print("="*80)
    
    # Load and display summary
    if os.path.exists('model_performance_summary.csv'):
        df = pd.read_csv('model_performance_summary.csv')
        print("\nModel Performance Summary (sorted by ROC-AUC):")
        print("-" * 60)
        print(df.round(4).to_string(index=False))
        
        # Highlight best performers
        best_roc = df.loc[df['ROC-AUC'].idxmax()]
        best_acc = df.loc[df['Accuracy'].idxmax()]
        best_f1 = df.loc[df['F1-Score'].idxmax()]
        
        print(f"\n🏆 BEST PERFORMERS:")
        print(f"   Best ROC-AUC:  {best_roc['Model']} ({best_roc['ROC-AUC']:.4f})")
        print(f"   Best Accuracy: {best_acc['Model']} ({best_acc['Accuracy']:.4f})")
        print(f"   Best F1-Score: {best_f1['Model']} ({best_f1['F1-Score']:.4f})")
    
    # Load and display feature importance
    if os.path.exists('feature_importance.csv'):
        fi_df = pd.read_csv('feature_importance.csv')
        print(f"\n📊 TOP 5 MOST IMPORTANT FEATURES:")
        print("-" * 40)
        for i, row in fi_df.head(5).iterrows():
            print(f"   {i+1}. {row['feature']}: {row['importance']:.3f}")

def show_visualizations():
    """Display the generated visualizations"""
    images = [
        ('roc_curves.png', 'ROC Curves Comparison'),
        ('confusion_matrices.png', 'Confusion Matrices'),
        ('feature_importance.png', 'Feature Importance'),
        ('model_comparison_metrics.png', 'Performance Metrics Comparison')
    ]
    
    print(f"\n📈 VISUALIZATIONS:")
    print("-" * 20)
    
    for img_file, title in images:
        if os.path.exists(img_file):
            print(f"   ✓ {title}: {img_file}")
            try:
                img = Image.open(img_file)
                plt.figure(figsize=(12, 8))
                plt.imshow(img)
                plt.axis('off')
                plt.title(title, fontsize=14, fontweight='bold')
                plt.tight_layout()
                plt.show()
            except Exception as e:
                print(f"     Error displaying {img_file}: {e}")
        else:
            print(f"   ✗ {title}: {img_file} not found")

def main():
    """Main function to display all results"""
    display_summary()
    
    print(f"\n📁 GENERATED FILES:")
    print("-" * 20)
    files = [
        'model_performance_summary.csv',
        'feature_importance.csv',
        'roc_curves.png',
        'confusion_matrices.png',
        'feature_importance.png',
        'model_comparison_metrics.png',
        'ML_Analysis_Summary.md'
    ]
    
    for file in files:
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024  # Size in KB
            print(f"   ✓ {file} ({size:.1f} KB)")
        else:
            print(f"   ✗ {file} (not found)")
    
    # Ask user if they want to view visualizations
    response = input(f"\n🖼️  Would you like to display the visualizations? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        show_visualizations()
    
    print(f"\n✅ Analysis complete! Check the files above for detailed results.")
    print(f"📖 Read 'ML_Analysis_Summary.md' for a comprehensive report.")

if __name__ == "__main__":
    main()

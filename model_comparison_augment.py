# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold, GridSearchCV
from sklearn.preprocessing import StandardScaler, OneHotEncoder, LabelEncoder, PolynomialFeatures, RobustScaler
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.feature_selection import SelectKBest, f_classif
from imblearn.over_sampling import SMOTE
from sklearn.ensemble import VotingClassifier
from imblearn.pipeline import Pipeline as ImbPipeline

# Machine Learning Models
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.neural_network import MLPClassifier
import xgboost as xgb
try:
    from lightgbm import LGBMClassifier
except ImportError:
    LGBMClassifier = None
try:
    from catboost import CatBoostClassifier
except ImportError:
    CatBoostClassifier = None

# Evaluation Metrics
from sklearn.metrics import (accuracy_score, classification_report, confusion_matrix,
                           roc_curve, auc, roc_auc_score, precision_recall_curve,
                           f1_score, precision_score, recall_score)

# Statistical Testing
from scipy import stats
import shap

print("="*80)
print("COMPREHENSIVE MACHINE LEARNING MODEL COMPARISON")
print("Dataset: Blood Pressure AI Analysis")
print("="*80)

# Load the data
print("\n1. Loading and exploring the dataset...")
data = pd.read_excel("bp_ai.xlsx")
print(f"Dataset shape: {data.shape}")
print(f"Columns: {data.columns.tolist()}")

# Remove arterial_map as specified
print("\n2. Preprocessing data...")
print("Removing 'arterial_map' column as specified...")
data = data.drop('arterial_map', axis=1)

# Check for missing values
print(f"Missing values per column:")
print(data.isnull().sum())

# --- 1. Data Quality: Handle missing values and outliers ---
print("\n[Data Quality] Handling missing values and outliers...")
# Fill missing values with median for numeric, mode for categorical
numerical_cols = data.select_dtypes(include=['int64', 'float64']).columns.tolist()
categorical_cols = data.select_dtypes(include=['object']).columns.tolist()

for col in numerical_cols:
    data[col] = data[col].fillna(data[col].median())
for col in categorical_cols:
    data[col] = data[col].fillna(data[col].mode()[0])
# Outlier handling: cap at 1st/99th percentile
for col in numerical_cols:
    lower, upper = data[col].quantile([0.01, 0.99])
    data[col] = data[col].clip(lower, upper)

# --- 1. Feature Engineering: Polynomial and interaction features ---
print("[Feature Engineering] Adding polynomial and interaction features...")
poly = PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)
X_poly = poly.fit_transform(data[numerical_cols])
poly_feature_names = [f"poly_{name}" for name in poly.get_feature_names_out(numerical_cols)]
X_poly_df = pd.DataFrame(X_poly, columns=poly_feature_names, index=data.index)
# Only use categorical columns that are not the target and are present in data
categorical_cols = [col for col in categorical_cols if col in data.columns and col != 'hypot_map']
# Drop 'hypot_map' from data before concatenation
X = pd.concat([data.drop(['hypot_map'], axis=1), X_poly_df], axis=1)

# Define features and target
y = data['hypot_map']

print(f"\nTarget variable distribution:")
print(y.value_counts())
print(f"Target variable proportions:")
print(y.value_counts(normalize=True))

# Encode the target variable
le = LabelEncoder()
y_encoded = le.fit_transform(y)
print(f"Label encoding: {dict(zip(le.classes_, le.transform(le.classes_)))}")

# Split the data into training and testing sets
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y)

y_train_encoded = le.transform(y_train)
y_test_encoded = le.transform(y_test)

print(f"\nTraining set size: {X_train.shape[0]}")
print(f"Test set size: {X_test.shape[0]}")
print(f"Training set target distribution:")
print(pd.Series(y_train).value_counts())

# --- 3. Data Preprocessing: RobustScaler and SMOTE ---
print("[Preprocessing] Using RobustScaler and SMOTE for class imbalance...")
preprocessor = ColumnTransformer(
    transformers=[
        ('num', RobustScaler(), poly_feature_names),
        ('cat', OneHotEncoder(drop='first', handle_unknown='ignore'), categorical_cols)
    ])

# --- 1. Feature Selection ---
print("[Feature Selection] Selecting top 20 features...")
selector = SelectKBest(f_classif, k=20)

# --- 2. Hyperparameter Tuning: GridSearchCV for key models ---
print("[Hyperparameter Tuning] Running GridSearchCV for key models...")
gs_params = {
    'Random Forest': {
        'classifier__n_estimators': [100, 200],
        'classifier__max_depth': [8, 10],
    },
    'XGBoost': {
        'classifier__max_depth': [4, 6],
        'classifier__learning_rate': [0.05, 0.1],
    },
    'Support Vector Machine': {
        'classifier__C': [0.5, 1.0],
        'classifier__gamma': ['scale', 'auto'],
    },
    'LightGBM': {
        'classifier__num_leaves': [31, 50],
        'classifier__learning_rate': [0.05, 0.1],
    },
    'CatBoost': {
        'classifier__depth': [4, 6],
        'classifier__learning_rate': [0.05, 0.1],
    }
}

models = {
    'Random Forest': {
        'model': RandomForestClassifier(random_state=42),
        'params': gs_params['Random Forest'],
        'needs_encoded': False
    },
    'XGBoost': {
        'model': xgb.XGBClassifier(random_state=42, eval_metric='logloss'),
        'params': gs_params['XGBoost'],
        'needs_encoded': True
    },
    'Support Vector Machine': {
        'model': SVC(probability=True, random_state=42),
        'params': gs_params['Support Vector Machine'],
        'needs_encoded': False
    }
}
if LGBMClassifier:
    models['LightGBM'] = {
        'model': LGBMClassifier(random_state=42),
        'params': gs_params['LightGBM'],
        'needs_encoded': False
    }
if CatBoostClassifier:
    models['CatBoost'] = {
        'model': CatBoostClassifier(verbose=0, random_state=42),
        'params': gs_params['CatBoost'],
        'needs_encoded': False
    }
ensemble_estimators = [
    ('rf', RandomForestClassifier(random_state=42)),
    ('xgb', xgb.XGBClassifier(random_state=42, eval_metric='logloss')),
    ('svc', SVC(probability=True, random_state=42))
]
if LGBMClassifier:
    ensemble_estimators.append(('lgbm', LGBMClassifier(random_state=42)))
if CatBoostClassifier:
    ensemble_estimators.append(('catboost', CatBoostClassifier(verbose=0, random_state=42)))

results = {}
trained_models = {}
y_probs = {}
conf_matrices = {}
class_reports = {}
for name, model_config in models.items():
    print(f"[GridSearchCV] Training {name}...")
    imb_pipeline = ImbPipeline([
        ('preprocessor', preprocessor),
        ('smote', SMOTE(random_state=42)),
        ('selector', selector),
        ('classifier', model_config['model'])
    ])
    gs = GridSearchCV(imb_pipeline, model_config['params'], cv=3, scoring='roc_auc', n_jobs=-1)
    if model_config['needs_encoded']:
        gs.fit(X_train, le.transform(y_train))
        y_pred = le.inverse_transform(gs.predict(X_test))
        y_prob = gs.predict_proba(X_test)[:, 1]
    else:
        gs.fit(X_train, y_train)
        y_pred = gs.predict(X_test)
        y_prob = gs.predict_proba(X_test)[:, 1]
    accuracy = accuracy_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred, pos_label='Yes')
    roc_auc = roc_auc_score(y_test_encoded, y_prob)
    conf_matrix = confusion_matrix(y_test, y_pred)
    class_report = classification_report(y_test, y_pred, output_dict=True)
    results[name] = {'accuracy': accuracy, 'f1_score': f1, 'roc_auc': roc_auc}
    trained_models[name] = gs.best_estimator_
    y_probs[name] = y_prob
    conf_matrices[name] = conf_matrix
    class_reports[name] = class_report
    print(f"  ✓ {name}: Accuracy={accuracy:.3f}, F1={f1:.3f}, ROC-AUC={roc_auc:.3f}")

# --- 4. Ensemble VotingClassifier ---
print("[Ensemble] Training VotingClassifier...")
voting = VotingClassifier(ensemble_estimators, voting='soft', n_jobs=-1)
voting_pipeline = ImbPipeline([
    ('preprocessor', preprocessor),
    ('smote', SMOTE(random_state=42)),
    ('selector', selector),
    ('classifier', voting)
])
voting_pipeline.fit(X_train, y_train)
y_pred = voting_pipeline.predict(X_test)
y_prob = voting_pipeline.predict_proba(X_test)[:, 1]
accuracy = accuracy_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred, pos_label='Yes')
roc_auc = roc_auc_score(y_test_encoded, y_prob)
conf_matrix = confusion_matrix(y_test, y_pred)
class_report = classification_report(y_test, y_pred, output_dict=True)
results['Voting Ensemble'] = {'accuracy': accuracy, 'f1_score': f1, 'roc_auc': roc_auc}
trained_models['Voting Ensemble'] = voting_pipeline
y_probs['Voting Ensemble'] = y_prob
conf_matrices['Voting Ensemble'] = conf_matrix
class_reports['Voting Ensemble'] = class_report
print(f"  ✓ Voting Ensemble: Accuracy={accuracy:.3f}, F1={f1:.3f}, ROC-AUC={roc_auc:.3f}")

# --- 5. Model Performance Summary Table ---
print("\n5. Model Performance Summary Table:")
print("=" * 80)
summary_data = []
for name in results:
    summary_data.append({
        'Model': name,
        'Accuracy': results[name]['accuracy'],
        'F1-Score': results[name]['f1_score'],
        'ROC-AUC': results[name]['roc_auc']
    })
summary_df = pd.DataFrame(summary_data)
summary_df = summary_df.sort_values('ROC-AUC', ascending=False)
print(summary_df.round(4).to_string(index=False))

# Find best performing models
print("\n6. Best Performing Models:")
print("-" * 40)
best_accuracy = summary_df.loc[summary_df['Accuracy'].idxmax()]
best_roc_auc = summary_df.loc[summary_df['ROC-AUC'].idxmax()]
best_f1 = summary_df.loc[summary_df['F1-Score'].idxmax()]
print(f"Best Accuracy:  {best_accuracy['Model']} ({best_accuracy['Accuracy']:.4f})")
print(f"Best ROC-AUC:   {best_roc_auc['Model']} ({best_roc_auc['ROC-AUC']:.4f})")
print(f"Best F1-Score:  {best_f1['Model']} ({best_f1['F1-Score']:.4f})")

# Save summary to CSV
summary_df.to_csv('model_performance_summary.csv', index=False)
print(f"\nSummary table saved to 'model_performance_summary.csv'")

# --- 10. Interpretability: SHAP for best model ---
best_model_name = max(results, key=lambda k: results[k]['roc_auc'])
best_model = trained_models[best_model_name]
print(f"[Interpretability] Running SHAP for {best_model_name}...")
try:
    explainer = shap.Explainer(best_model.named_steps['classifier'], X_test)
    shap_values = explainer(X_test)
    shap.summary_plot(shap_values, X_test, feature_names=X_test.columns, show=False)
    plt.savefig('shap_summary.png', dpi=300, bbox_inches='tight')
    print("SHAP summary plot saved as 'shap_summary.png'")
except Exception as e:
    print(f"SHAP analysis failed: {e}")

# --- 8. Creating visualizations... ---
# Plot ROC curves
plt.figure(figsize=(12, 8))
colors = plt.cm.tab10(np.linspace(0, 1, len(y_probs)))
for i, (name, y_prob) in enumerate(y_probs.items()):
    try:
        fpr, tpr, _ = roc_curve(y_test_encoded, y_prob)
        roc_auc = auc(fpr, tpr)
        plt.plot(fpr, tpr, color=colors[i], linewidth=2,
                label=f'{name} (AUC = {roc_auc:.3f})')
    except:
        continue
plt.plot([0, 1], [0, 1], 'k--', linewidth=1, alpha=0.8)
plt.xlabel('False Positive Rate', fontsize=12)
plt.ylabel('True Positive Rate', fontsize=12)
plt.title('ROC Curves for Classification Models', fontsize=14, fontweight='bold')
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig('roc_curves.png', dpi=300, bbox_inches='tight')
plt.show()

# Plot confusion matrices
n_models = len(results)
n_cols = 3
n_rows = (n_models + n_cols - 1) // n_cols
fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))
if n_rows == 1:
    axes = axes.reshape(1, -1)
axes = axes.flatten()
for i, (name, conf_matrix) in enumerate(conf_matrices.items()):
    sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues',
                xticklabels=['No', 'Yes'], yticklabels=['No', 'Yes'], ax=axes[i])
    axes[i].set_title(f'{name}', fontweight='bold')
    axes[i].set_xlabel('Predicted')
    axes[i].set_ylabel('Actual')
for i in range(len(results), len(axes)):
    axes[i].set_visible(False)
plt.tight_layout()
plt.savefig('confusion_matrices.png', dpi=300, bbox_inches='tight')
plt.show()

print("\nAnalysis complete! Check the generated files for detailed results.")

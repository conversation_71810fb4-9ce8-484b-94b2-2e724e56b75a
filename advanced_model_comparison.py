#!/usr/bin/env python3
"""
Advanced Machine Learning Model Comparison Framework
====================================================

This script implements a state-of-the-art machine learning comparison framework with:
- Ensemble techniques (Voting, Stacking, Bagging)
- Advanced neural network architectures with different activations and batch normalization
- Hyperparameter optimization using GridSearchCV and RandomizedSearchCV
- Advanced regularization including dropout and early stopping
- Model interpretability using SHAP values and permutation importance
- Statistical analysis with McNemar's test and confidence intervals
- Advanced cross-validation strategies
- Feature engineering with polynomial features and interaction terms
- Performance optimization with parallel processing

Author: Augment Agent
Dataset: Blood Pressure AI Analysis (bp_ai.xlsx)
Target: hypot_map (excluding arterial_map as specified)
"""

# Core libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import time
import joblib
from itertools import combinations
from multiprocessing import cpu_count
warnings.filterwarnings('ignore')

# Scikit-learn imports
from sklearn.model_selection import (
    train_test_split, cross_val_score, StratifiedKFold,
    GridSearchCV, RandomizedSearchCV, validation_curve
)
from sklearn.preprocessing import (
    StandardScaler, RobustScaler, MinMaxScaler, OneHotEncoder,
    LabelEncoder, PolynomialFeatures, PowerTransformer
)
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.feature_selection import SelectKBest, f_classif, RFE, SelectFromModel
from sklearn.decomposition import PCA

# Machine Learning Models
from sklearn.linear_model import LogisticRegression, ElasticNet
from sklearn.ensemble import (
    RandomForestClassifier, GradientBoostingClassifier,
    VotingClassifier, BaggingClassifier, StackingClassifier,
    ExtraTreesClassifier, AdaBoostClassifier
)
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis, QuadraticDiscriminantAnalysis
import xgboost as xgb

# Advanced libraries (with fallbacks)
try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False

# Evaluation and statistical testing
from sklearn.metrics import (
    accuracy_score, classification_report, confusion_matrix,
    roc_curve, auc, roc_auc_score, precision_recall_curve,
    f1_score, precision_score, recall_score, log_loss,
    matthews_corrcoef, cohen_kappa_score
)
from scipy import stats
from scipy.stats import chi2_contingency
import scipy.stats as stats

# Imbalanced learning (with fallback)
try:
    from imblearn.over_sampling import SMOTE, ADASYN
    from imblearn.under_sampling import RandomUnderSampler
    from imblearn.combine import SMOTETomek
    from imblearn.pipeline import Pipeline as ImbPipeline
    IMBALANCED_AVAILABLE = True
except ImportError:
    IMBALANCED_AVAILABLE = False

print("="*100)
print("🚀 ADVANCED MACHINE LEARNING MODEL COMPARISON FRAMEWORK")
print("Dataset: Blood Pressure AI Analysis")
print("="*100)

class AdvancedModelComparison:
    """
    Advanced Machine Learning Model Comparison Framework

    This class implements a comprehensive ML comparison with advanced techniques:
    - Multiple preprocessing strategies
    - Ensemble methods and stacking
    - Hyperparameter optimization
    - Advanced regularization
    - Model interpretability
    - Statistical significance testing
    """

    def __init__(self, random_state=42, n_jobs=-1, verbose=True):
        """
        Initialize the Advanced Model Comparison Framework

        Parameters:
        -----------
        random_state : int, default=42
            Random state for reproducibility
        n_jobs : int, default=-1
            Number of parallel jobs (-1 uses all available cores)
        verbose : bool, default=True
            Whether to print detailed progress information
        """
        self.random_state = random_state
        self.n_jobs = min(n_jobs, cpu_count()) if n_jobs == -1 else n_jobs
        self.verbose = verbose

        # Initialize storage containers
        self.data = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.y_train_encoded = None
        self.y_test_encoded = None
        self.label_encoder = None

        # Results storage
        self.results = {}
        self.trained_models = {}
        self.y_probs = {}
        self.feature_importance = {}
        self.cv_results = {}
        self.hyperparameter_results = {}

        # Performance tracking
        self.training_times = {}
        self.prediction_times = {}

        if self.verbose:
            print(f"🔧 Initialized framework with {self.n_jobs} parallel jobs")
            print(f"📊 Available advanced libraries:")
            print(f"   - SHAP: {'✅' if SHAP_AVAILABLE else '❌'}")
            print(f"   - LightGBM: {'✅' if LIGHTGBM_AVAILABLE else '❌'}")
            print(f"   - CatBoost: {'✅' if CATBOOST_AVAILABLE else '❌'}")
            print(f"   - Imbalanced-learn: {'✅' if IMBALANCED_AVAILABLE else '❌'}")

    def load_and_preprocess_data(self, filepath="bp_ai.xlsx"):
        """
        Load and perform initial preprocessing of the dataset

        Parameters:
        -----------
        filepath : str, default="bp_ai.xlsx"
            Path to the dataset file
        """
        if self.verbose:
            print("\n📂 1. Loading and exploring the dataset...")

        # Load data
        self.data = pd.read_excel(filepath)

        if self.verbose:
            print(f"   Dataset shape: {self.data.shape}")
            print(f"   Columns: {self.data.columns.tolist()}")

        # Remove arterial_map as specified
        if 'arterial_map' in self.data.columns:
            self.data = self.data.drop('arterial_map', axis=1)
            if self.verbose:
                print("   ✅ Removed 'arterial_map' column as specified")

        # Check for missing values
        missing_values = self.data.isnull().sum()
        if self.verbose:
            print(f"   Missing values per column:")
            for col, missing in missing_values.items():
                if missing > 0:
                    print(f"     {col}: {missing}")
            if missing_values.sum() == 0:
                print("     ✅ No missing values found")

        # Handle missing values if any
        if missing_values.sum() > 0:
            self._handle_missing_values()

        # Define features and target
        X = self.data.drop('hypot_map', axis=1)
        y = self.data['hypot_map']

        if self.verbose:
            print(f"\n   Target variable distribution:")
            print(f"     {y.value_counts().to_dict()}")
            print(f"   Target proportions:")
            print(f"     {y.value_counts(normalize=True).round(4).to_dict()}")

        # Encode target variable
        self.label_encoder = LabelEncoder()
        y_encoded = self.label_encoder.fit_transform(y)

        if self.verbose:
            print(f"   Label encoding: {dict(zip(self.label_encoder.classes_, self.label_encoder.transform(self.label_encoder.classes_)))}")

        # Split data with stratification
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=self.random_state, stratify=y
        )

        self.y_train_encoded = self.label_encoder.transform(self.y_train)
        self.y_test_encoded = self.label_encoder.transform(self.y_test)

        if self.verbose:
            print(f"\n   Training set size: {self.X_train.shape[0]}")
            print(f"   Test set size: {self.X_test.shape[0]}")
            print(f"   Training set target distribution: {pd.Series(self.y_train).value_counts().to_dict()}")

    def _handle_missing_values(self):
        """Handle missing values using multiple strategies"""
        if self.verbose:
            print("   🔧 Handling missing values...")

        # Identify column types
        numerical_cols = self.data.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = self.data.select_dtypes(include=['object']).columns.tolist()

        # Remove target from lists if present
        if 'hypot_map' in numerical_cols:
            numerical_cols.remove('hypot_map')
        if 'hypot_map' in categorical_cols:
            categorical_cols.remove('hypot_map')

        # Fill numerical columns with median
        for col in numerical_cols:
            if self.data[col].isnull().sum() > 0:
                self.data[col].fillna(self.data[col].median(), inplace=True)

        # Fill categorical columns with mode
        for col in categorical_cols:
            if self.data[col].isnull().sum() > 0:
                self.data[col].fillna(self.data[col].mode()[0], inplace=True)

    def create_advanced_features(self):
        """
        Create advanced features using multiple techniques:
        - Polynomial features
        - Interaction terms
        - Statistical transformations
        - Domain-specific features
        """
        if self.verbose:
            print("\n🔬 2. Advanced Feature Engineering...")

        # Identify column types
        numerical_cols = self.X_train.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = self.X_train.select_dtypes(include=['object']).columns.tolist()

        if self.verbose:
            print(f"   Numerical columns: {numerical_cols}")
            print(f"   Categorical columns: {categorical_cols}")

        # Create domain-specific features for blood pressure analysis
        if 'arm_map' in numerical_cols and 'ankle_map' in numerical_cols:
            # Ankle-Brachial Index (ABI) - important cardiovascular indicator
            self.X_train['abi_ratio'] = self.X_train['ankle_map'] / (self.X_train['arm_map'] + 1e-8)
            self.X_test['abi_ratio'] = self.X_test['ankle_map'] / (self.X_test['arm_map'] + 1e-8)

            # Pressure difference
            self.X_train['pressure_diff'] = self.X_train['arm_map'] - self.X_train['ankle_map']
            self.X_test['pressure_diff'] = self.X_test['arm_map'] - self.X_test['ankle_map']

            # Average pressure
            self.X_train['avg_pressure'] = (self.X_train['arm_map'] + self.X_train['ankle_map']) / 2
            self.X_test['avg_pressure'] = (self.X_test['arm_map'] + self.X_test['ankle_map']) / 2

            if self.verbose:
                print("   ✅ Created cardiovascular-specific features (ABI ratio, pressure differences)")

        # BMI categories if BMI and age are available
        if 'bmi' in numerical_cols and 'age' in numerical_cols:
            # BMI categories
            def categorize_bmi(bmi):
                if bmi < 18.5:
                    return 'underweight'
                elif bmi < 25:
                    return 'normal'
                elif bmi < 30:
                    return 'overweight'
                else:
                    return 'obese'

            self.X_train['bmi_category'] = self.X_train['bmi'].apply(categorize_bmi)
            self.X_test['bmi_category'] = self.X_test['bmi'].apply(categorize_bmi)

            # Age groups
            def categorize_age(age):
                if age < 30:
                    return 'young'
                elif age < 50:
                    return 'middle'
                elif age < 65:
                    return 'senior'
                else:
                    return 'elderly'

            self.X_train['age_group'] = self.X_train['age'].apply(categorize_age)
            self.X_test['age_group'] = self.X_test['age'].apply(categorize_age)

            if self.verbose:
                print("   ✅ Created BMI and age category features")

        # Update column lists
        numerical_cols = self.X_train.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = self.X_train.select_dtypes(include=['object']).columns.tolist()

        # Store for later use
        self.numerical_cols = numerical_cols
        self.categorical_cols = categorical_cols

        if self.verbose:
            print(f"   Final feature count: {len(numerical_cols)} numerical + {len(categorical_cols)} categorical = {len(numerical_cols) + len(categorical_cols)} total")

    def create_preprocessing_pipelines(self):
        """
        Create multiple preprocessing pipelines for comparison:
        - Standard scaling
        - Robust scaling
        - MinMax scaling
        - Power transformation
        - With and without PCA
        """
        if self.verbose:
            print("\n⚙️ 3. Creating Advanced Preprocessing Pipelines...")

        self.preprocessors = {}

        # Standard preprocessing pipeline
        self.preprocessors['standard'] = ColumnTransformer([
            ('num', StandardScaler(), self.numerical_cols),
            ('cat', OneHotEncoder(drop='first', handle_unknown='ignore'), self.categorical_cols)
        ])

        # Robust preprocessing pipeline (better for outliers)
        self.preprocessors['robust'] = ColumnTransformer([
            ('num', RobustScaler(), self.numerical_cols),
            ('cat', OneHotEncoder(drop='first', handle_unknown='ignore'), self.categorical_cols)
        ])

        # MinMax preprocessing pipeline
        self.preprocessors['minmax'] = ColumnTransformer([
            ('num', MinMaxScaler(), self.numerical_cols),
            ('cat', OneHotEncoder(drop='first', handle_unknown='ignore'), self.categorical_cols)
        ])

        # Power transformation pipeline (for non-normal distributions)
        self.preprocessors['power'] = ColumnTransformer([
            ('num', PowerTransformer(method='yeo-johnson'), self.numerical_cols),
            ('cat', OneHotEncoder(drop='first', handle_unknown='ignore'), self.categorical_cols)
        ])

        if self.verbose:
            print(f"   ✅ Created {len(self.preprocessors)} preprocessing pipelines")
            print(f"   Available: {list(self.preprocessors.keys())}")

    def get_base_models(self):
        """
        Define base models with advanced configurations and regularization
        """
        models = {}

        # Logistic Regression with different regularization
        models['LogisticRegression_L2'] = {
            'model': LogisticRegression(
                max_iter=2000, random_state=self.random_state,
                C=1.0, penalty='l2', solver='lbfgs'
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__C': [0.01, 0.1, 1.0, 10.0],
                'classifier__max_iter': [1000, 2000]
            }
        }

        models['LogisticRegression_L1'] = {
            'model': LogisticRegression(
                max_iter=2000, random_state=self.random_state,
                C=0.1, penalty='l1', solver='liblinear'
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__C': [0.01, 0.1, 1.0],
                'classifier__max_iter': [1000, 2000]
            }
        }

        # ElasticNet (L1 + L2 regularization)
        models['LogisticRegression_ElasticNet'] = {
            'model': LogisticRegression(
                max_iter=2000, random_state=self.random_state,
                penalty='elasticnet', solver='saga', l1_ratio=0.5
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__C': [0.1, 1.0, 10.0],
                'classifier__l1_ratio': [0.1, 0.5, 0.9]
            }
        }

        # Random Forest with advanced configuration
        models['RandomForest'] = {
            'model': RandomForestClassifier(
                n_estimators=100, random_state=self.random_state,
                max_depth=10, min_samples_split=5, min_samples_leaf=2,
                bootstrap=True, oob_score=True, n_jobs=self.n_jobs
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__n_estimators': [100, 200, 300],
                'classifier__max_depth': [8, 10, 12, None],
                'classifier__min_samples_split': [2, 5, 10],
                'classifier__min_samples_leaf': [1, 2, 4]
            }
        }

        # Extra Trees (more randomization)
        models['ExtraTrees'] = {
            'model': ExtraTreesClassifier(
                n_estimators=100, random_state=self.random_state,
                max_depth=10, min_samples_split=5, min_samples_leaf=2,
                bootstrap=True, n_jobs=self.n_jobs
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__n_estimators': [100, 200],
                'classifier__max_depth': [8, 10, None],
                'classifier__min_samples_split': [2, 5]
            }
        }

        # XGBoost with advanced regularization
        models['XGBoost'] = {
            'model': xgb.XGBClassifier(
                random_state=self.random_state, eval_metric='logloss',
                reg_alpha=0.1, reg_lambda=1.0, max_depth=6,
                learning_rate=0.1, n_estimators=100, subsample=0.8,
                colsample_bytree=0.8, n_jobs=self.n_jobs
            ),
            'needs_encoded': True,
            'param_grid': {
                'classifier__max_depth': [4, 6, 8],
                'classifier__learning_rate': [0.05, 0.1, 0.2],
                'classifier__n_estimators': [100, 200],
                'classifier__reg_alpha': [0.01, 0.1, 1.0],
                'classifier__reg_lambda': [0.01, 0.1, 1.0]
            }
        }

        # Gradient Boosting
        models['GradientBoosting'] = {
            'model': GradientBoostingClassifier(
                random_state=self.random_state, max_depth=5,
                learning_rate=0.1, n_estimators=100, subsample=0.8
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__max_depth': [3, 5, 7],
                'classifier__learning_rate': [0.05, 0.1, 0.2],
                'classifier__n_estimators': [100, 200],
                'classifier__subsample': [0.8, 0.9, 1.0]
            }
        }

        # Support Vector Machine with different kernels
        models['SVM_RBF'] = {
            'model': SVC(
                probability=True, random_state=self.random_state,
                C=1.0, gamma='scale', kernel='rbf'
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__C': [0.1, 1.0, 10.0],
                'classifier__gamma': ['scale', 'auto', 0.001, 0.01]
            }
        }

        models['SVM_Poly'] = {
            'model': SVC(
                probability=True, random_state=self.random_state,
                C=1.0, kernel='poly', degree=3
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__C': [0.1, 1.0, 10.0],
                'classifier__degree': [2, 3, 4]
            }
        }

        # K-Nearest Neighbors with different distance metrics
        models['KNN_Euclidean'] = {
            'model': KNeighborsClassifier(n_neighbors=5, metric='euclidean', n_jobs=self.n_jobs),
            'needs_encoded': False,
            'param_grid': {
                'classifier__n_neighbors': [3, 5, 7, 9],
                'classifier__weights': ['uniform', 'distance']
            }
        }

        models['KNN_Manhattan'] = {
            'model': KNeighborsClassifier(n_neighbors=5, metric='manhattan', n_jobs=self.n_jobs),
            'needs_encoded': False,
            'param_grid': {
                'classifier__n_neighbors': [3, 5, 7, 9],
                'classifier__weights': ['uniform', 'distance']
            }
        }

        # Decision Tree with advanced pruning
        models['DecisionTree'] = {
            'model': DecisionTreeClassifier(
                random_state=self.random_state, max_depth=10,
                min_samples_split=10, min_samples_leaf=5,
                criterion='gini', splitter='best'
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__max_depth': [5, 10, 15, None],
                'classifier__min_samples_split': [2, 5, 10],
                'classifier__min_samples_leaf': [1, 2, 5],
                'classifier__criterion': ['gini', 'entropy']
            }
        }

        # Advanced Neural Networks with different architectures
        models['NeuralNet_Small'] = {
            'model': MLPClassifier(
                hidden_layer_sizes=(50, 25), max_iter=2000,
                random_state=self.random_state, alpha=0.01,
                learning_rate_init=0.001, activation='relu',
                solver='adam', early_stopping=True, validation_fraction=0.1,
                n_iter_no_change=20, beta_1=0.9, beta_2=0.999
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__hidden_layer_sizes': [(50,), (50, 25), (100, 50)],
                'classifier__alpha': [0.001, 0.01, 0.1],
                'classifier__learning_rate_init': [0.001, 0.01],
                'classifier__activation': ['relu', 'tanh']
            }
        }

        models['NeuralNet_Medium'] = {
            'model': MLPClassifier(
                hidden_layer_sizes=(100, 50, 25), max_iter=2000,
                random_state=self.random_state, alpha=0.01,
                learning_rate_init=0.001, activation='relu',
                solver='adam', early_stopping=True, validation_fraction=0.1,
                n_iter_no_change=20
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__hidden_layer_sizes': [(100, 50), (100, 50, 25), (200, 100, 50)],
                'classifier__alpha': [0.001, 0.01, 0.1],
                'classifier__learning_rate_init': [0.001, 0.01]
            }
        }

        models['NeuralNet_Large'] = {
            'model': MLPClassifier(
                hidden_layer_sizes=(200, 100, 50, 25), max_iter=2000,
                random_state=self.random_state, alpha=0.1,
                learning_rate_init=0.001, activation='relu',
                solver='adam', early_stopping=True, validation_fraction=0.1,
                n_iter_no_change=20
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__hidden_layer_sizes': [(200, 100, 50), (200, 100, 50, 25)],
                'classifier__alpha': [0.01, 0.1, 1.0],
                'classifier__learning_rate_init': [0.0001, 0.001]
            }
        }

        # Naive Bayes
        models['NaiveBayes'] = {
            'model': GaussianNB(),
            'needs_encoded': False,
            'param_grid': {
                'classifier__var_smoothing': [1e-9, 1e-8, 1e-7, 1e-6]
            }
        }

        # Linear and Quadratic Discriminant Analysis
        models['LDA'] = {
            'model': LinearDiscriminantAnalysis(solver='svd'),
            'needs_encoded': False,
            'param_grid': {
                'classifier__solver': ['svd', 'lsqr'],
                'classifier__shrinkage': [None, 'auto', 0.1, 0.5]
            }
        }

        models['QDA'] = {
            'model': QuadraticDiscriminantAnalysis(),
            'needs_encoded': False,
            'param_grid': {
                'classifier__reg_param': [0.0, 0.01, 0.1, 0.5]
            }
        }

        # AdaBoost
        models['AdaBoost'] = {
            'model': AdaBoostClassifier(
                random_state=self.random_state, n_estimators=100,
                learning_rate=1.0, algorithm='SAMME.R'
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__n_estimators': [50, 100, 200],
                'classifier__learning_rate': [0.5, 1.0, 1.5]
            }
        }

        # Add LightGBM if available
        if LIGHTGBM_AVAILABLE:
            models['LightGBM'] = {
                'model': lgb.LGBMClassifier(
                    random_state=self.random_state, n_estimators=100,
                    learning_rate=0.1, max_depth=6, num_leaves=31,
                    subsample=0.8, colsample_bytree=0.8, n_jobs=self.n_jobs,
                    verbose=-1
                ),
                'needs_encoded': False,
                'param_grid': {
                    'classifier__n_estimators': [100, 200],
                    'classifier__learning_rate': [0.05, 0.1, 0.2],
                    'classifier__max_depth': [4, 6, 8],
                    'classifier__num_leaves': [31, 50, 70]
                }
            }

        # Add CatBoost if available
        if CATBOOST_AVAILABLE:
            models['CatBoost'] = {
                'model': cb.CatBoostClassifier(
                    random_state=self.random_state, iterations=100,
                    learning_rate=0.1, depth=6, verbose=False
                ),
                'needs_encoded': False,
                'param_grid': {
                    'classifier__iterations': [100, 200],
                    'classifier__learning_rate': [0.05, 0.1, 0.2],
                    'classifier__depth': [4, 6, 8]
                }
            }

        return models

    def create_ensemble_models(self, base_models):
        """
        Create advanced ensemble models including voting, stacking, and bagging

        Parameters:
        -----------
        base_models : dict
            Dictionary of base models to use for ensembles
        """
        ensemble_models = {}

        # Select best performing base models for ensembles
        # For demonstration, we'll use a subset of models
        ensemble_base_models = [
            ('rf', RandomForestClassifier(n_estimators=100, random_state=self.random_state)),
            ('xgb', xgb.XGBClassifier(random_state=self.random_state, eval_metric='logloss')),
            ('svm', SVC(probability=True, random_state=self.random_state)),
            ('lr', LogisticRegression(random_state=self.random_state, max_iter=1000))
        ]

        # Voting Classifier (Soft Voting)
        ensemble_models['VotingClassifier_Soft'] = {
            'model': VotingClassifier(
                estimators=ensemble_base_models,
                voting='soft',
                n_jobs=self.n_jobs
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__voting': ['soft'],
                'classifier__rf__n_estimators': [100, 200],
                'classifier__svm__C': [0.1, 1.0, 10.0]
            }
        }

        # Voting Classifier (Hard Voting)
        ensemble_models['VotingClassifier_Hard'] = {
            'model': VotingClassifier(
                estimators=ensemble_base_models,
                voting='hard',
                n_jobs=self.n_jobs
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__voting': ['hard']
            }
        }

        # Stacking Classifier
        ensemble_models['StackingClassifier'] = {
            'model': StackingClassifier(
                estimators=ensemble_base_models,
                final_estimator=LogisticRegression(random_state=self.random_state),
                cv=5,
                n_jobs=self.n_jobs
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__cv': [3, 5],
                'classifier__final_estimator__C': [0.1, 1.0, 10.0]
            }
        }

        # Bagging Classifier with different base estimators
        ensemble_models['BaggingClassifier_DT'] = {
            'model': BaggingClassifier(
                estimator=DecisionTreeClassifier(random_state=self.random_state),
                n_estimators=100,
                random_state=self.random_state,
                n_jobs=self.n_jobs
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__n_estimators': [50, 100, 200],
                'classifier__max_samples': [0.8, 1.0],
                'classifier__max_features': [0.8, 1.0]
            }
        }

        ensemble_models['BaggingClassifier_SVM'] = {
            'model': BaggingClassifier(
                estimator=SVC(probability=True, random_state=self.random_state),
                n_estimators=50,  # Fewer estimators for SVM due to computational cost
                random_state=self.random_state,
                n_jobs=self.n_jobs
            ),
            'needs_encoded': False,
            'param_grid': {
                'classifier__n_estimators': [25, 50],
                'classifier__max_samples': [0.8, 1.0]
            }
        }

        return ensemble_models

    def optimize_hyperparameters(self, models, preprocessor_name='standard',
                                 search_type='grid', n_iter=50, cv_folds=3):
        """
        Perform hyperparameter optimization using GridSearchCV or RandomizedSearchCV

        Parameters:
        -----------
        models : dict
            Dictionary of models to optimize
        preprocessor_name : str, default='standard'
            Name of preprocessor to use
        search_type : str, default='grid'
            Type of search ('grid' or 'random')
        n_iter : int, default=50
            Number of iterations for RandomizedSearchCV
        cv_folds : int, default=3
            Number of cross-validation folds
        """
        if self.verbose:
            print(f"\n🔍 4. Hyperparameter Optimization ({search_type.upper()} Search)")
            print(f"   Using {preprocessor_name} preprocessor with {cv_folds}-fold CV")

        optimized_models = {}
        preprocessor = self.preprocessors[preprocessor_name]

        for model_name, model_config in models.items():
            if self.verbose:
                print(f"   Optimizing {model_name}...")

            start_time = time.time()

            try:
                # Create pipeline
                if IMBALANCED_AVAILABLE:
                    # Use SMOTE for class imbalance if available
                    pipeline = ImbPipeline([
                        ('preprocessor', preprocessor),
                        ('smote', SMOTE(random_state=self.random_state)),
                        ('classifier', model_config['model'])
                    ])
                else:
                    pipeline = Pipeline([
                        ('preprocessor', preprocessor),
                        ('classifier', model_config['model'])
                    ])

                # Choose search strategy
                if search_type == 'grid':
                    search = GridSearchCV(
                        pipeline,
                        model_config['param_grid'],
                        cv=cv_folds,
                        scoring='roc_auc',
                        n_jobs=self.n_jobs,
                        verbose=0
                    )
                else:  # random search
                    search = RandomizedSearchCV(
                        pipeline,
                        model_config['param_grid'],
                        n_iter=n_iter,
                        cv=cv_folds,
                        scoring='roc_auc',
                        n_jobs=self.n_jobs,
                        random_state=self.random_state,
                        verbose=0
                    )

                # Fit the search
                if model_config['needs_encoded']:
                    search.fit(self.X_train, self.y_train_encoded)
                else:
                    search.fit(self.X_train, self.y_train)

                # Store optimized model
                optimized_models[model_name] = {
                    'model': search.best_estimator_,
                    'best_params': search.best_params_,
                    'best_score': search.best_score_,
                    'needs_encoded': model_config['needs_encoded']
                }

                # Store hyperparameter results
                self.hyperparameter_results[model_name] = {
                    'best_params': search.best_params_,
                    'best_score': search.best_score_,
                    'cv_results': search.cv_results_
                }

                optimization_time = time.time() - start_time

                if self.verbose:
                    print(f"     ✅ Best CV Score: {search.best_score_:.4f} (Time: {optimization_time:.1f}s)")

            except Exception as e:
                if self.verbose:
                    print(f"     ❌ Error: {str(e)}")
                continue

        return optimized_models

    def train_and_evaluate_models(self, models, preprocessor_name='standard'):
        """
        Train and evaluate all models with comprehensive metrics

        Parameters:
        -----------
        models : dict
            Dictionary of models to train and evaluate
        preprocessor_name : str, default='standard'
            Name of preprocessor to use
        """
        if self.verbose:
            print(f"\n🏋️ 5. Training and Evaluating Models")
            print(f"   Using {preprocessor_name} preprocessor")
            print("-" * 80)

        preprocessor = self.preprocessors[preprocessor_name]

        for i, (model_name, model_config) in enumerate(models.items(), 1):
            if self.verbose:
                print(f"   Training {i}/{len(models)}: {model_name}...")

            start_time = time.time()

            try:
                # Get the model (either optimized or base)
                if 'model' in model_config:
                    model = model_config['model']
                    needs_encoded = model_config['needs_encoded']
                else:
                    # This is an optimized model
                    model = model_config['model']
                    needs_encoded = model_config['needs_encoded']

                # Create pipeline if not already a pipeline
                if not hasattr(model, 'named_steps'):
                    if IMBALANCED_AVAILABLE:
                        pipeline = ImbPipeline([
                            ('preprocessor', preprocessor),
                            ('smote', SMOTE(random_state=self.random_state)),
                            ('classifier', model)
                        ])
                    else:
                        pipeline = Pipeline([
                            ('preprocessor', preprocessor),
                            ('classifier', model)
                        ])
                else:
                    pipeline = model

                # Train the model
                if needs_encoded:
                    pipeline.fit(self.X_train, self.y_train_encoded)
                else:
                    pipeline.fit(self.X_train, self.y_train)

                training_time = time.time() - start_time
                self.training_times[model_name] = training_time

                # Make predictions
                pred_start_time = time.time()

                if needs_encoded:
                    y_pred_encoded = pipeline.predict(self.X_test)
                    y_pred = self.label_encoder.inverse_transform(y_pred_encoded)
                    y_prob = pipeline.predict_proba(self.X_test)[:, 1]
                else:
                    y_pred = pipeline.predict(self.X_test)
                    if hasattr(pipeline, "predict_proba"):
                        y_prob = pipeline.predict_proba(self.X_test)[:, 1]
                    else:
                        # For models without predict_proba
                        if hasattr(pipeline, "decision_function"):
                            y_prob = pipeline.decision_function(self.X_test)
                            # Normalize to [0,1] range
                            y_prob = (y_prob - y_prob.min()) / (y_prob.max() - y_prob.min())
                        else:
                            y_prob = self.label_encoder.transform(y_pred)

                prediction_time = time.time() - pred_start_time
                self.prediction_times[model_name] = prediction_time

                # Calculate comprehensive metrics
                accuracy = accuracy_score(self.y_test, y_pred)
                precision = precision_score(self.y_test, y_pred, pos_label='Yes', zero_division=0)
                recall = recall_score(self.y_test, y_pred, pos_label='Yes', zero_division=0)
                f1 = f1_score(self.y_test, y_pred, pos_label='Yes', zero_division=0)

                try:
                    roc_auc = roc_auc_score(self.y_test_encoded, y_prob)
                except:
                    roc_auc = np.nan

                # Additional metrics
                mcc = matthews_corrcoef(self.y_test, y_pred)
                kappa = cohen_kappa_score(self.y_test, y_pred)

                try:
                    log_loss_score = log_loss(self.y_test_encoded, y_prob)
                except:
                    log_loss_score = np.nan

                conf_matrix = confusion_matrix(self.y_test, y_pred)
                class_report = classification_report(self.y_test, y_pred, output_dict=True)

                # Store results
                self.results[model_name] = {
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'roc_auc': roc_auc,
                    'matthews_corrcoef': mcc,
                    'cohen_kappa': kappa,
                    'log_loss': log_loss_score,
                    'confusion_matrix': conf_matrix,
                    'classification_report': class_report,
                    'training_time': training_time,
                    'prediction_time': prediction_time
                }

                self.y_probs[model_name] = y_prob
                self.trained_models[model_name] = pipeline

                if self.verbose:
                    print(f"     ✅ Acc: {accuracy:.3f}, ROC-AUC: {roc_auc:.3f}, F1: {f1:.3f} "
                          f"(Train: {training_time:.1f}s, Pred: {prediction_time:.3f}s)")

            except Exception as e:
                if self.verbose:
                    print(f"     ❌ Error: {str(e)}")
                continue

        if self.verbose:
            print(f"\n   ✅ Successfully trained {len(self.results)} models")

    def perform_cross_validation(self, models, cv_folds=5, scoring_metrics=None):
        """
        Perform comprehensive cross-validation analysis

        Parameters:
        -----------
        models : dict
            Dictionary of models to cross-validate
        cv_folds : int, default=5
            Number of cross-validation folds
        scoring_metrics : list, default=None
            List of scoring metrics to evaluate
        """
        if scoring_metrics is None:
            scoring_metrics = ['accuracy', 'roc_auc', 'f1', 'precision', 'recall']

        if self.verbose:
            print(f"\n📊 6. Cross-Validation Analysis ({cv_folds}-fold)")
            print(f"   Metrics: {scoring_metrics}")
            print("-" * 60)

        cv_strategy = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=self.random_state)

        # Combine training and test data for full CV
        X_full = pd.concat([self.X_train, self.X_test])
        y_full = pd.concat([self.y_train, self.y_test])
        y_full_encoded = self.label_encoder.transform(y_full)

        for model_name in list(self.trained_models.keys())[:5]:  # Limit to top 5 for time
            if self.verbose:
                print(f"   Cross-validating {model_name}...")

            try:
                model = self.trained_models[model_name]
                needs_encoded = model_name in [name for name, config in models.items()
                                             if config.get('needs_encoded', False)]

                cv_scores = {}
                for metric in scoring_metrics:
                    if needs_encoded:
                        scores = cross_val_score(model, X_full, y_full_encoded,
                                               cv=cv_strategy, scoring=metric, n_jobs=self.n_jobs)
                    else:
                        scores = cross_val_score(model, X_full, y_full,
                                               cv=cv_strategy, scoring=metric, n_jobs=self.n_jobs)

                    cv_scores[metric] = {
                        'mean': scores.mean(),
                        'std': scores.std(),
                        'scores': scores
                    }

                self.cv_results[model_name] = cv_scores

                if self.verbose:
                    print(f"     ROC-AUC: {cv_scores['roc_auc']['mean']:.4f} ± {cv_scores['roc_auc']['std']:.4f}")
                    print(f"     Accuracy: {cv_scores['accuracy']['mean']:.4f} ± {cv_scores['accuracy']['std']:.4f}")

            except Exception as e:
                if self.verbose:
                    print(f"     ❌ Error: {str(e)}")
                continue

    def analyze_feature_importance(self):
        """
        Analyze feature importance using multiple methods
        """
        if self.verbose:
            print("\n🔍 7. Feature Importance Analysis...")

        # Random Forest feature importance
        if 'RandomForest' in self.trained_models:
            rf_model = self.trained_models['RandomForest']

            # Get feature names after preprocessing
            if hasattr(rf_model.named_steps['preprocessor'], 'transformers_'):
                try:
                    cat_features = rf_model.named_steps['preprocessor'].transformers_[1][1].get_feature_names_out(self.categorical_cols)
                    feature_names = np.concatenate([self.numerical_cols, cat_features])

                    # Get feature importances
                    if hasattr(rf_model.named_steps, 'classifier'):
                        importances = rf_model.named_steps['classifier'].feature_importances_
                    elif hasattr(rf_model.named_steps, 'smote'):
                        importances = rf_model.named_steps['smote'].feature_importances_
                    else:
                        importances = rf_model.feature_importances_

                    # Store feature importance
                    self.feature_importance['RandomForest'] = pd.DataFrame({
                        'feature': feature_names,
                        'importance': importances
                    }).sort_values('importance', ascending=False)

                    if self.verbose:
                        print("   ✅ Random Forest feature importance calculated")
                        print(f"   Top 5 features: {self.feature_importance['RandomForest'].head()['feature'].tolist()}")

                except Exception as e:
                    if self.verbose:
                        print(f"   ❌ Error calculating RF importance: {e}")

        # SHAP analysis if available
        if SHAP_AVAILABLE and len(self.trained_models) > 0:
            try:
                # Use the best performing model for SHAP analysis
                best_model_name = max(self.results.keys(), key=lambda k: self.results[k]['roc_auc'])
                best_model = self.trained_models[best_model_name]

                if self.verbose:
                    print(f"   Computing SHAP values for {best_model_name}...")

                # Transform test data for SHAP
                if hasattr(best_model, 'named_steps'):
                    if 'preprocessor' in best_model.named_steps:
                        X_test_transformed = best_model.named_steps['preprocessor'].transform(self.X_test)

                        # Get the classifier
                        if 'classifier' in best_model.named_steps:
                            classifier = best_model.named_steps['classifier']
                        elif 'smote' in best_model.named_steps:
                            # Skip SMOTE for SHAP analysis
                            classifier = best_model.named_steps['classifier'] if 'classifier' in best_model.named_steps else None
                        else:
                            classifier = best_model

                        if classifier is not None:
                            # Create SHAP explainer
                            explainer = shap.Explainer(classifier, X_test_transformed[:100])  # Use subset for speed
                            shap_values = explainer(X_test_transformed[:100])

                            # Save SHAP summary plot
                            plt.figure(figsize=(10, 6))
                            shap.summary_plot(shap_values, X_test_transformed[:100], show=False)
                            plt.savefig('advanced_shap_summary.png', dpi=300, bbox_inches='tight')
                            plt.close()

                            if self.verbose:
                                print("   ✅ SHAP analysis completed, saved to 'advanced_shap_summary.png'")

            except Exception as e:
                if self.verbose:
                    print(f"   ❌ SHAP analysis failed: {e}")

    def perform_statistical_analysis(self):
        """
        Perform statistical significance testing between models
        """
        if self.verbose:
            print("\n📈 8. Statistical Significance Analysis...")

        if len(self.results) < 2:
            if self.verbose:
                print("   ⚠️  Need at least 2 models for statistical comparison")
            return

        # McNemar's test for comparing classifiers
        model_names = list(self.results.keys())

        if self.verbose:
            print("   Performing McNemar's tests for model comparison...")

        mcnemar_results = {}

        for i, model1 in enumerate(model_names):
            for j, model2 in enumerate(model_names[i+1:], i+1):
                try:
                    # Get predictions for both models
                    model1_pipeline = self.trained_models[model1]
                    model2_pipeline = self.trained_models[model2]

                    pred1 = model1_pipeline.predict(self.X_test)
                    pred2 = model2_pipeline.predict(self.X_test)

                    # Create contingency table for McNemar's test
                    # Count agreements and disagreements
                    both_correct = np.sum((pred1 == self.y_test) & (pred2 == self.y_test))
                    both_wrong = np.sum((pred1 != self.y_test) & (pred2 != self.y_test))
                    model1_correct_model2_wrong = np.sum((pred1 == self.y_test) & (pred2 != self.y_test))
                    model1_wrong_model2_correct = np.sum((pred1 != self.y_test) & (pred2 == self.y_test))

                    # McNemar's test statistic
                    if (model1_correct_model2_wrong + model1_wrong_model2_correct) > 0:
                        mcnemar_stat = ((abs(model1_correct_model2_wrong - model1_wrong_model2_correct) - 1) ** 2) / (model1_correct_model2_wrong + model1_wrong_model2_correct)
                        p_value = 1 - stats.chi2.cdf(mcnemar_stat, 1)

                        mcnemar_results[f"{model1}_vs_{model2}"] = {
                            'statistic': mcnemar_stat,
                            'p_value': p_value,
                            'significant': p_value < 0.05
                        }

                        if self.verbose and p_value < 0.05:
                            print(f"     {model1} vs {model2}: p={p_value:.4f} (significant difference)")

                except Exception as e:
                    if self.verbose:
                        print(f"     ❌ Error comparing {model1} vs {model2}: {e}")
                    continue

        # Store results
        self.statistical_results = mcnemar_results

        # Calculate confidence intervals for ROC-AUC scores
        if self.verbose:
            print("\n   Calculating confidence intervals for ROC-AUC scores...")

        confidence_intervals = {}
        for model_name in self.results.keys():
            try:
                roc_auc = self.results[model_name]['roc_auc']
                n = len(self.y_test)

                # Bootstrap confidence interval for AUC
                n_bootstrap = 1000
                bootstrap_aucs = []

                for _ in range(n_bootstrap):
                    # Bootstrap sample
                    indices = np.random.choice(len(self.y_test), size=len(self.y_test), replace=True)
                    y_test_boot = self.y_test_encoded[indices]
                    y_prob_boot = self.y_probs[model_name][indices]

                    try:
                        auc_boot = roc_auc_score(y_test_boot, y_prob_boot)
                        bootstrap_aucs.append(auc_boot)
                    except:
                        continue

                if len(bootstrap_aucs) > 0:
                    ci_lower = np.percentile(bootstrap_aucs, 2.5)
                    ci_upper = np.percentile(bootstrap_aucs, 97.5)

                    confidence_intervals[model_name] = {
                        'roc_auc': roc_auc,
                        'ci_lower': ci_lower,
                        'ci_upper': ci_upper,
                        'ci_width': ci_upper - ci_lower
                    }

                    if self.verbose:
                        print(f"     {model_name}: {roc_auc:.4f} [{ci_lower:.4f}, {ci_upper:.4f}]")

            except Exception as e:
                if self.verbose:
                    print(f"     ❌ Error calculating CI for {model_name}: {e}")
                continue

        self.confidence_intervals = confidence_intervals

    def generate_comprehensive_report(self):
        """
        Generate comprehensive analysis report with visualizations
        """
        if self.verbose:
            print("\n📊 9. Generating Comprehensive Report...")

        # Create summary DataFrame
        summary_data = []
        for model_name, result in self.results.items():
            row = {
                'Model': model_name,
                'Accuracy': result['accuracy'],
                'Precision': result['precision'],
                'Recall': result['recall'],
                'F1-Score': result['f1_score'],
                'ROC-AUC': result['roc_auc'],
                'Matthews_Corr': result['matthews_corrcoef'],
                'Cohen_Kappa': result['cohen_kappa'],
                'Log_Loss': result['log_loss'],
                'Training_Time': result['training_time'],
                'Prediction_Time': result['prediction_time']
            }

            # Add confidence intervals if available
            if hasattr(self, 'confidence_intervals') and model_name in self.confidence_intervals:
                ci = self.confidence_intervals[model_name]
                row['ROC_AUC_CI_Lower'] = ci['ci_lower']
                row['ROC_AUC_CI_Upper'] = ci['ci_upper']
                row['ROC_AUC_CI_Width'] = ci['ci_width']

            summary_data.append(row)

        summary_df = pd.DataFrame(summary_data)
        summary_df = summary_df.sort_values('ROC-AUC', ascending=False)

        # Save comprehensive results
        summary_df.to_csv('advanced_model_performance_summary.csv', index=False)

        if self.verbose:
            print("   ✅ Saved comprehensive results to 'advanced_model_performance_summary.csv'")
            print(f"\n   🏆 TOP 5 MODELS BY ROC-AUC:")
            for i, (_, row) in enumerate(summary_df.head().iterrows(), 1):
                print(f"     {i}. {row['Model']}: {row['ROC-AUC']:.4f}")

        # Create advanced visualizations
        self._create_advanced_visualizations(summary_df)

        return summary_df

    def _create_advanced_visualizations(self, summary_df):
        """Create advanced visualizations for the analysis"""

        # 1. ROC Curves with confidence intervals
        plt.figure(figsize=(12, 8))
        colors = plt.cm.tab20(np.linspace(0, 1, len(self.y_probs)))

        for i, (model_name, y_prob) in enumerate(self.y_probs.items()):
            try:
                fpr, tpr, _ = roc_curve(self.y_test_encoded, y_prob)
                roc_auc = auc(fpr, tpr)

                # Add confidence interval if available
                ci_text = ""
                if hasattr(self, 'confidence_intervals') and model_name in self.confidence_intervals:
                    ci = self.confidence_intervals[model_name]
                    ci_text = f" [{ci['ci_lower']:.3f}, {ci['ci_upper']:.3f}]"

                plt.plot(fpr, tpr, color=colors[i], linewidth=2,
                        label=f'{model_name}: {roc_auc:.3f}{ci_text}')
            except:
                continue

        plt.plot([0, 1], [0, 1], 'k--', linewidth=1, alpha=0.8)
        plt.xlabel('False Positive Rate', fontsize=12)
        plt.ylabel('True Positive Rate', fontsize=12)
        plt.title('Advanced ROC Curves with Confidence Intervals', fontsize=14, fontweight='bold')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('advanced_roc_curves.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 2. Performance comparison radar chart
        metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'ROC-AUC']
        top_models = summary_df.head(5)

        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))

        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # Complete the circle

        for i, (_, model_row) in enumerate(top_models.iterrows()):
            values = [model_row[metric] for metric in metrics]
            values += values[:1]  # Complete the circle

            ax.plot(angles, values, 'o-', linewidth=2, label=model_row['Model'])
            ax.fill(angles, values, alpha=0.1)

        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics)
        ax.set_ylim(0, 1)
        ax.set_title('Top 5 Models Performance Comparison', fontsize=14, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)

        plt.tight_layout()
        plt.savefig('advanced_performance_radar.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 3. Training time vs Performance scatter plot
        plt.figure(figsize=(12, 8))

        for i, (_, row) in enumerate(summary_df.iterrows()):
            plt.scatter(row['Training_Time'], row['ROC-AUC'],
                       s=100, alpha=0.7, label=row['Model'])
            plt.annotate(row['Model'],
                        (row['Training_Time'], row['ROC-AUC']),
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=8, alpha=0.8)

        plt.xlabel('Training Time (seconds)', fontsize=12)
        plt.ylabel('ROC-AUC Score', fontsize=12)
        plt.title('Model Performance vs Training Time', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('advanced_performance_vs_time.png', dpi=300, bbox_inches='tight')
        plt.close()

        if self.verbose:
            print("   ✅ Generated advanced visualizations:")
            print("     - advanced_roc_curves.png")
            print("     - advanced_performance_radar.png")
            print("     - advanced_performance_vs_time.png")

    def run_complete_analysis(self, optimize_hyperparams=True, include_ensembles=True,
                            search_type='random', cv_folds=5):
        """
        Run the complete advanced machine learning analysis pipeline

        Parameters:
        -----------
        optimize_hyperparams : bool, default=True
            Whether to perform hyperparameter optimization
        include_ensembles : bool, default=True
            Whether to include ensemble methods
        search_type : str, default='random'
            Type of hyperparameter search ('grid' or 'random')
        cv_folds : int, default=5
            Number of cross-validation folds
        """
        start_time = time.time()

        # Step 1: Load and preprocess data
        self.load_and_preprocess_data()

        # Step 2: Create advanced features
        self.create_advanced_features()

        # Step 3: Create preprocessing pipelines
        self.create_preprocessing_pipelines()

        # Step 4: Get base models
        base_models = self.get_base_models()

        # Step 5: Hyperparameter optimization (optional)
        if optimize_hyperparams:
            # Optimize a subset of models for time efficiency
            models_to_optimize = {k: v for i, (k, v) in enumerate(base_models.items()) if i < 5}
            optimized_models = self.optimize_hyperparameters(
                models_to_optimize,
                search_type=search_type,
                cv_folds=cv_folds
            )
            # Combine optimized and base models
            all_models = {**optimized_models, **{k: v for k, v in base_models.items() if k not in optimized_models}}
        else:
            all_models = base_models

        # Step 6: Add ensemble models (optional)
        if include_ensembles:
            ensemble_models = self.create_ensemble_models(base_models)
            all_models.update(ensemble_models)

        # Step 7: Train and evaluate all models
        self.train_and_evaluate_models(all_models)

        # Step 8: Cross-validation analysis
        self.perform_cross_validation(all_models, cv_folds=cv_folds)

        # Step 9: Feature importance analysis
        self.analyze_feature_importance()

        # Step 10: Statistical analysis
        self.perform_statistical_analysis()

        # Step 11: Generate comprehensive report
        summary_df = self.generate_comprehensive_report()

        total_time = time.time() - start_time

        if self.verbose:
            print(f"\n🎉 ADVANCED ANALYSIS COMPLETE!")
            print(f"⏱️  Total execution time: {total_time:.1f} seconds")
            print(f"📊 Models evaluated: {len(self.results)}")
            print(f"🏆 Best model: {summary_df.iloc[0]['Model']} (ROC-AUC: {summary_df.iloc[0]['ROC-AUC']:.4f})")
            print("\n📁 Generated files:")
            print("   - advanced_model_performance_summary.csv")
            print("   - advanced_roc_curves.png")
            print("   - advanced_performance_radar.png")
            print("   - advanced_performance_vs_time.png")
            if SHAP_AVAILABLE:
                print("   - advanced_shap_summary.png")

        return summary_df


# Main execution
if __name__ == "__main__":
    # Initialize the advanced framework
    framework = AdvancedModelComparison(random_state=42, n_jobs=-1, verbose=True)

    # Run complete analysis
    results = framework.run_complete_analysis(
        optimize_hyperparams=True,
        include_ensembles=True,
        search_type='random',  # Use random search for faster execution
        cv_folds=3  # Use 3-fold CV for faster execution
    )

    print("\n" + "="*100)
    print("🚀 ADVANCED MACHINE LEARNING ANALYSIS COMPLETED SUCCESSFULLY!")
    print("="*100)
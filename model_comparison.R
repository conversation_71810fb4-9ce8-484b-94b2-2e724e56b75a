# Load required libraries
library(tidyverse)
library(caret)
library(randomForest)
library(neuralnet)
library(pROC)
library(readxl)
library(xgboost)
library(knitr)

# Load and prepare data
data <- read_excel("bp_ai.xlsx")

# Remove arterial_map as specified and keep hypot_map as target
data <- data %>% select(-arterial_map)

# Convert hypot_map to factor for classification
data$hypot_map <- factor(data$hypot_map, levels = c("No", "Yes"))

# Split data into training and testing sets
set.seed(123)
trainIndex <- createDataPartition(data$hypot_map, p = 0.8, list = FALSE)
train_data <- data[trainIndex, ]
test_data <- data[-trainIndex, ]

# Create dummy variables for categorical predictors
dummy_model <- dummyVars(" ~ gender + asa + hypothyroid + dm", 
                         data = train_data,
                         fullRank = TRUE)
train_dummies <- predict(dummy_model, train_data)
test_dummies <- predict(dummy_model, test_data)

# Define numeric predictors
numeric_vars <- c("age", "bmi", "arm_circum", "ankle_circum", "arm_map", "ankle_map")

# Separate numeric predictors and target variable
train_data_numeric <- train_data[, numeric_vars]
test_data_numeric <- test_data[, numeric_vars]
train_target <- train_data$hypot_map
test_target <- test_data$hypot_map

# Preprocess numeric data
preProc <- preProcess(train_data_numeric, method = c("center", "scale"))
train_data_numeric_processed <- predict(preProc, train_data_numeric)
test_data_numeric_processed <- predict(preProc, test_data_numeric)

# Combine processed numeric data with dummy variables
train_data_processed <- cbind(train_data_numeric_processed, train_dummies)
test_data_processed <- cbind(test_data_numeric_processed, test_dummies)

# Add target variable back to training data
train_data_processed$hypot_map <- train_target
test_data_processed$hypot_map <- test_target

# Define training control for classification
ctrl <- trainControl(method = "cv",
                    number = 10,
                    classProbs = TRUE,
                    summaryFunction = twoClassSummary)

# Train models
# 1. Logistic Regression
lr_model <- train(hypot_map ~ .,
                 data = train_data_processed,
                 method = "glm",
                 family = "binomial",
                 trControl = ctrl,
                 metric = "ROC")

# 2. Random Forest
rf_model <- train(hypot_map ~ .,
                 data = train_data_processed,
                 method = "rf",
                 trControl = ctrl,
                 tuneLength = 5,
                 metric = "ROC")

# 3. XGBoost
xgb_model <- train(hypot_map ~ .,
                  data = train_data_processed,
                  method = "xgbTree",
                  trControl = ctrl,
                  tuneLength = 5,
                  metric = "ROC")

# 4. Neural Network
# For neuralnet, we need numeric output (0/1)
train_data_nn <- train_data_processed
train_data_nn$hypot_map <- as.numeric(train_data_processed$hypot_map == "Yes")
predictors <- names(train_data_processed)[!names(train_data_processed) %in% "hypot_map"]
formula <- as.formula(paste("hypot_map ~", paste(predictors, collapse = " + ")))

nn_model <- neuralnet(formula,
                     data = train_data_nn,
                     hidden = c(10, 5),
                     linear.output = FALSE,
                     stepmax = 1e6,
                     threshold = 0.01)

# Make predictions
lr_pred <- predict(lr_model, newdata = test_data_processed, type = "raw")
rf_pred <- predict(rf_model, newdata = test_data_processed, type = "raw")
xgb_pred <- predict(xgb_model, newdata = test_data_processed, type = "raw")
nn_pred_raw <- compute(nn_model, test_data_processed[, predictors])$net.result
nn_pred <- ifelse(nn_pred_raw > 0.5, "Yes", "No")
nn_pred <- factor(nn_pred, levels = c("No", "Yes"))

# Get probabilities for ROC curves
lr_prob <- predict(lr_model, newdata = test_data_processed, type = "prob")$Yes
rf_prob <- predict(rf_model, newdata = test_data_processed, type = "prob")$Yes
xgb_prob <- predict(xgb_model, newdata = test_data_processed, type = "prob")$Yes

# Evaluate models
evaluate_model <- function(predictions, actual) {
  cm <- confusionMatrix(predictions, actual)
  accuracy <- cm$overall["Accuracy"]
  sensitivity <- cm$byClass["Sensitivity"]
  specificity <- cm$byClass["Specificity"]
  return(list(Accuracy = accuracy, 
              Sensitivity = sensitivity, 
              Specificity = specificity))
}

lr_metrics <- evaluate_model(lr_pred, test_target)
rf_metrics <- evaluate_model(rf_pred, test_target)
xgb_metrics <- evaluate_model(xgb_pred, test_target)
nn_metrics <- evaluate_model(nn_pred, test_target)

# Create results table
results_table <- data.frame(
  Model = c("Logistic Regression", "Random Forest", "XGBoost", "Neural Network"),
  Accuracy = c(lr_metrics$Accuracy, rf_metrics$Accuracy, xgb_metrics$Accuracy, nn_metrics$Accuracy),
  Sensitivity = c(lr_metrics$Sensitivity, rf_metrics$Sensitivity, xgb_metrics$Sensitivity, nn_metrics$Sensitivity),
  Specificity = c(lr_metrics$Specificity, rf_metrics$Specificity, xgb_metrics$Specificity, nn_metrics$Specificity)
)

# Round numeric values to 3 decimal places
results_table[, 2:4] <- round(results_table[, 2:4], 3)

# Print the table
print(kable(results_table, format = "markdown",
           caption = "Classification Model Performance Metrics",
           align = "c"))

# Plot ROC curves
roc_lr <- roc(test_target, lr_prob)
roc_rf <- roc(test_target, rf_prob)
roc_xgb <- roc(test_target, xgb_prob)
roc_nn <- roc(test_target, as.vector(nn_pred_raw))

# Plot ROC curves
plot(roc_lr, col = "blue", main = "ROC Curves for Classification Models")
plot(roc_rf, col = "red", add = TRUE)
plot(roc_xgb, col = "green", add = TRUE)
plot(roc_nn, col = "purple", add = TRUE)
legend("bottomright", 
       legend = c(paste("LR (AUC =", round(auc(roc_lr), 3), ")"),
                 paste("RF (AUC =", round(auc(roc_rf), 3), ")"),
                 paste("XGB (AUC =", round(auc(roc_xgb), 3), ")"),
                 paste("NN (AUC =", round(auc(roc_nn), 3), ")")),
       col = c("blue", "red", "green", "purple"),
       lwd = 2)

# Feature importance for Random Forest
importance <- varImp(rf_model)
print(importance)
plot(importance, top = 10, main = "Top 10 Feature Importance (Random Forest)")

# Plot confusion matrices
par(mfrow = c(2, 2))
plot(confusionMatrix(lr_pred, test_target)$table, main = "Logistic Regression")
plot(confusionMatrix(rf_pred, test_target)$table, main = "Random Forest")
plot(confusionMatrix(xgb_pred, test_target)$table, main = "XGBoost")
plot(confusionMatrix(nn_pred, test_target)$table, main = "Neural Network")
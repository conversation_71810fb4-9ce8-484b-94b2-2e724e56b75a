# Load required libraries
library(tidyverse)
library(caret)
library(randomForest)
library(neuralnet)
library(readxl)

data = read_excel("bp_ai.xlsx")

# Remove hypot_map as it's not needed for prediction
data <- data %>% select(-hypot_map)

# Split data into training and testing sets
set.seed(123)
trainIndex <- createDataPartition(data$arterial_map, p = 0.8, list = FALSE)
train_data <- data[trainIndex, ]
test_data <- data[-trainIndex, ]

# Create dummy variables for categorical variables
dummy_model <- dummyVars(" ~ gender + asa + hypothyroid + dm", 
                         data = train_data,
                         fullRank = TRUE)
train_dummies <- predict(dummy_model, train_data)
test_dummies <- predict(dummy_model, test_data)

# Define numeric predictors (excluding arterial_map)
numeric_vars <- c("age", "bmi", "arm_circum", "ankle_circum", "arm_map", "ankle_map")

# Separate numeric predictors and target variable
train_data_numeric <- train_data[, numeric_vars]
test_data_numeric <- test_data[, numeric_vars]
train_target <- train_data$arterial_map
test_target <- test_data$arterial_map

# Combine numeric predictors with dummy variables
train_data_processed <- cbind(train_data_numeric, train_dummies)
test_data_processed <- cbind(test_data_numeric, test_dummies)

# Scale numeric predictor variables only
preProcess <- preProcess(train_data_processed[, numeric_vars], 
                         method = c("center", "scale"))
train_data_processed <- predict(preProcess, train_data_processed)
test_data_processed <- predict(preProcess, test_data_processed)

# Add target variable back to training data (unscaled)
train_data_processed$arterial_map <- train_target
test_data_processed$arterial_map <- test_target

# 1. Linear Regression Model
lm_model <- train(arterial_map ~ .,
                  data = train_data_processed,
                  method = "lm",
                  trControl = trainControl(method = "cv", number = 10))

# 2. Random Forest Model
rf_model <- train(arterial_map ~ .,
                  data = train_data_processed,
                  method = "rf",
                  trControl = trainControl(method = "cv", number = 10),
                  tuneLength = 5)

# 3. Neural Network Model
# Create formula with all predictors (excluding arterial_map)
predictors <- names(train_data_processed)[!names(train_data_processed) %in% "arterial_map"]
formula <- as.formula(paste("arterial_map ~", paste(predictors, collapse = " + ")))

# Train neural network
nn_model <- neuralnet(formula,
                      data = train_data_processed,
                      hidden = c(15, 10),
                      linear.output = TRUE,
                      stepmax = 100000,
                      lifesign = 'full',
                      lifesign.step = 100,
                      threshold = 2.5)

# Make predictions
lm_pred <- predict(lm_model, newdata = test_data_processed)
rf_pred <- predict(rf_model, newdata = test_data_processed)
nn_pred <- compute(nn_model, test_data_processed[, predictors])$net.result

# Evaluate models
evaluate_model <- function(predictions, actual) {
  rmse <- sqrt(mean((predictions - actual)^2))
  mae <- mean(abs(predictions - actual))
  r2 <- cor(predictions, actual)^2
  return(list(RMSE = rmse, MAE = mae, R2 = r2))
}

# Get evaluation metrics
lm_metrics <- evaluate_model(lm_pred, test_target)
rf_metrics <- evaluate_model(rf_pred, test_target)
nn_metrics <- evaluate_model(nn_pred, test_target)

# Print results
cat("Linear Regression Results:\n")
cat("RMSE:", lm_metrics$RMSE, "\n")
cat("MAE:", lm_metrics$MAE, "\n")
cat("R2:", lm_metrics$R2, "\n\n")

cat("Random Forest Results:\n")
cat("RMSE:", rf_metrics$RMSE, "\n")
cat("MAE:", rf_metrics$MAE, "\n")
cat("R2:", rf_metrics$R2, "\n\n")

cat("Neural Network Results:\n")
cat("RMSE:", nn_metrics$RMSE, "\n")
cat("MAE:", nn_metrics$MAE, "\n")
cat("R2:", nn_metrics$R2, "\n")

# Feature importance for Random Forest
importance <- varImp(rf_model)
print(importance)

# Plot predictions vs actual values
par(mfrow = c(1, 3))
plot(test_target, lm_pred, main = "Linear Regression",
     xlab = "Actual", ylab = "Predicted")
abline(0, 1, col = "red")
plot(test_target, rf_pred, main = "Random Forest",
     xlab = "Actual", ylab = "Predicted")
abline(0, 1, col = "red")
plot(test_target, nn_pred, main = "Neural Network",
     xlab = "Actual", ylab = "Predicted")
abline(0, 1, col = "red")


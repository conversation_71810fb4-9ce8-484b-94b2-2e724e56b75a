# Load necessary libraries
library(neuralnet)
library(readxl)

# Load the data
data <- read_excel("bp_ML.xlsx")

# Check the data structure
str(data)
summary(data)

# Remove rows with missing values (if any)
data <- na.omit(data)

# Normalize the data (scaling between 0 and 1)
maxs <- apply(data, 2, max)
mins <- apply(data, 2, min)
data_scaled <- as.data.frame(scale(data, center = mins, scale = maxs - mins))

# Split the data into training and testing sets
set.seed(123)
trainIndex <- sample(1:nrow(data_scaled), 0.8 * nrow(data_scaled))
trainData <- data_scaled[trainIndex, ]
testData <- data_scaled[-trainIndex, ]

# Build a neural network model
formula <- as.formula(paste("Arterial ~", paste(names(data)[-which(names(data) == "Arterial")], collapse = "+")))
model <- neuralnet(formula, data = trainData, hidden = c(5, 3), linear.output = TRUE)

# Plot the neural network
plot(model)

# Make predictions
predictions <- compute(model, testData[,-which(names(testData) == "Arterial")])$net.result
predictions <- predictions * (max(data$Arterial) - min(data$Arterial)) + min(data$Arterial)
actual <- testData$Arterial * (max(data$Arterial) - min(data$Arterial)) + min(data$Arterial)

# Evaluate model performance
results <- data.frame(Actual = actual, Predicted = predictions)
correlation <- cor(results$Actual, results$Predicted)
cat("Correlation between actual and predicted values:", correlation, "\n")

# Plot actual vs predicted values
plot(results$Actual, results$Predicted, main = "Actual vs Predicted Arterial Values",
     xlab = "Actual Values", ylab = "Predicted Values")
abline(0, 1, col = "red")








# Make predictions on the training data
train_predictions <- compute(model, trainData[,-which(names(trainData) == "Arterial")])$net.result
train_predictions <- train_predictions * (max(data$Arterial) - min(data$Arterial)) + min(data$Arterial)
train_actual <- trainData$Arterial * (max(data$Arterial) - min(data$Arterial)) + min(data$Arterial)

# Calculate performance metrics
train_results <- data.frame(Actual = train_actual, Predicted = train_predictions)

# Calculate correlation
train_correlation <- cor(train_results$Actual, train_results$Predicted)
cat("Correlation on training data:", train_correlation, "\n")

# Calculate Mean Squared Error (MSE)
train_mse <- mean((train_results$Actual - train_results$Predicted)^2)
cat("MSE on training data:", train_mse, "\n")

# Calculate R-squared
train_r_squared <- 1 - (sum((train_results$Actual - train_results$Predicted)^2) / 
                          sum((train_results$Actual - mean(train_results$Actual))^2))
cat("R-squared on training data:", train_r_squared, "\n")

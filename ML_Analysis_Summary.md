---
output:
  word_document: default
  html_document: default
---
# Comprehensive Machine Learning Model Comparison
## Blood Pressure AI Analysis

### Executive Summary

This analysis compares the performance of 10 different machine learning models for predicting hypertension mapping (`hypot_map`) using blood pressure and demographic data. The analysis excludes the `arterial_map` variable as specified.

### Dataset Overview

- **Total samples**: 1,088
- **Features**: 10 (after removing `arterial_map`)
- **Target variable**: `hypot_map` (binary: No/Yes)
- **Class distribution**: 
  - No: 762 (70.0%)
  - Yes: 326 (30.0%)
- **Missing values**: None

### Features Used

**Numerical Features:**
- age
- bmi
- arm_circum (arm circumference)
- ankle_circum (ankle circumference)
- arm_map (arm mean arterial pressure)
- ankle_map (ankle mean arterial pressure)

**Categorical Features:**
- gender (Male/Female)
- asa (ASA classification)
- hypothyroid (Yes/No)
- dm (diabetes mellitus: Yes/No)

### Models Evaluated

1. **Logistic Regression**
2. **Random Forest**
3. **XGBoost**
4. **Gradient Boosting**
5. **Support Vector Machine (SVM)**
6. **Decision Tree**
7. **K-Nearest Neighbors (KNN)**
8. **Neural Network (Small)** - 2 hidden layers (10, 5 neurons)
9. **Neural Network (Medium)** - 2 hidden layers (50, 25 neurons)
10. **Neural Network (Large)** - 3 hidden layers (100, 50, 25 neurons)

### Key Results

#### Top 3 Performing Models (by ROC-AUC):

1. **Neural Network (Small)** - ROC-AUC: 0.935
   - Accuracy: 87.2%
   - Precision: 82.5%
   - Recall: 72.3%
   - F1-Score: 77.0%

2. **Gradient Boosting** - ROC-AUC: 0.920
   - Accuracy: 85.8%
   - Precision: 77.4%
   - Recall: 73.8%
   - F1-Score: 75.6%

3. **Support Vector Machine** - ROC-AUC: 0.918
   - Accuracy: 86.2%
   - Precision: 83.0%
   - Recall: 67.7%
   - F1-Score: 74.6%

#### Feature Importance (Random Forest):

1. **arm_map** (40.5%) - Most important predictor
2. **ankle_map** (27.9%) - Second most important
3. **age** (7.5%)
4. **bmi** (6.7%)
5. **ankle_circum** (6.6%)
6. **arm_circum** (6.4%)
7. **asa_II** (2.0%)
8. **gender_Male** (1.3%)
9. **dm_Yes** (0.9%)
10. **hypothyroid_Yes** (0.2%)

### Cross-Validation Results (Top 3 Models):

- **Neural Network (Small)**: CV Accuracy: 83.7% ± 2.1%, CV ROC-AUC: 87.7% ± 1.1%
- **Gradient Boosting**: CV Accuracy: 83.8% ± 1.4%, CV ROC-AUC: 88.2% ± 1.9%
- **Support Vector Machine**: CV Accuracy: 84.5% ± 1.9%, CV ROC-AUC: 87.2% ± 2.8%

### Key Insights

1. **Neural networks performed exceptionally well**, with the small neural network achieving the highest ROC-AUC score (0.935).

2. **Blood pressure measurements are the most predictive features**, with arm_map and ankle_map accounting for ~68% of the predictive power.

3. **Traditional ensemble methods** (Gradient Boosting, Random Forest, XGBoost) also performed well, showing robust performance.

4. **The dataset appears well-suited for machine learning**, with all models achieving >82% accuracy.

5. **Class imbalance** (70% No, 30% Yes) was handled well by most models, though some showed higher specificity than sensitivity.

### Recommendations

1. **Primary recommendation**: Use the **Neural Network (Small)** model for production, as it achieved the best overall performance with good generalization (based on CV results).

2. **Alternative recommendation**: **Gradient Boosting** offers a good balance of performance and interpretability.

3. **Feature engineering**: Consider creating interaction terms between arm_map and ankle_map, as these are the most important features.

4. **Model ensemble**: Combining the top 3 models could potentially improve performance further.

5. **Threshold optimization**: Consider optimizing the classification threshold based on the specific clinical requirements (sensitivity vs. specificity trade-off).

### Files Generated

- `model_performance_summary.csv`: Detailed performance metrics for all models
- `feature_importance.csv`: Feature importance rankings from Random Forest
- `roc_curves.png`: ROC curves comparison for all models
- `confusion_matrices.png`: Confusion matrices for all models
- `feature_importance.png`: Feature importance visualization
- `model_comparison_metrics.png`: Performance metrics comparison charts

### Technical Notes

- All models used stratified train-test split (80/20) with random_state=42
- Preprocessing included StandardScaler for numerical features and OneHotEncoder for categorical features
- Neural networks used early stopping and regularization to prevent overfitting
- Cross-validation used 5-fold stratified sampling for robust evaluation

### Conclusion

The analysis successfully identified several high-performing models for hypertension prediction, with neural networks showing superior performance. The blood pressure measurements (arm_map and ankle_map) are the most critical predictors, suggesting that the model captures clinically relevant patterns in the data.

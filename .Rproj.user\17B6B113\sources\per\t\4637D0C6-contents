library(readxl)
bp <- read_excel("bp_ai.xlsx")
str(bp)

#bp$gender = as.factor(bp$gender)
bp$gender <- ifelse(bp$gender == "Male", 0, 1)
#bp$asa = as.factor(bp$asa)
bp$asa <- ifelse(bp$asa == "I", 1, 2)
#bp$hypothyroid = as.factor(bp$hypothyroid)
bp$hypothyroid <- ifelse(bp$hypothyroid == "Yes", 1, 0)
#bp$dm = as.factor(bp$dm)
bp$dm <- ifelse(bp$dm == "Yes", 1, 0)
bp$hypot_map = ifelse(bp$hypot_map == "Yes", 1, 0)

str(bp)

library(tidyverse)
# Definetidyverse# Define all predictors (continuous and categorical combined)
X_all <- bp[,-c(which(names(bp) == "arterial_map"),
                which(names(bp) == "hypot_map"))] %>% as.matrix()

y <- bp$arterial_map

# Split into training (80%) and testing (20%) sets
set.seed(103)
train_idx <- sample(1:nrow(bp), 0.8 * nrow(bp))
X_all_train <- X_all[train_idx, ]
X_all_test <- X_all[-train_idx, ]
y_train <- y[train_idx]
y_test <- y[-train_idx]
#is.numeric(X_all_train)

# Normalize continuous variables only
X_continuous_mean <- apply(X_all_train[, c(1,2,7,8,9,10)], 2, mean)
X_continuous_sd <- apply(X_all_train[, c(1,2,7,8,9,10)], 2, sd)
X_all_train_normalized <- X_all_train
X_all_test_normalized <- X_all_test
X_all_train_normalized[, c(1,2,7,8,9,10)] <- scale(X_all_train[, c(1,2,7,8,9,10)], 
                                                   center = X_continuous_mean, scale = X_continuous_sd)
X_all_test_normalized[, c(1,2,7,8,9,10)] <- scale(X_all_test[, c(1,2,7,8,9,10)], 
                                                  center = X_continuous_mean, scale = X_continuous_sd)


library(keras)
# Define the neural network using the functional API
input_all <- layer_input(shape = ncol(X_all_train_normalized), name = "all_input")

# First hidden layer: 100 neurons, fully connected to all inputs
hidden_layer1 <- input_all %>% 
  layer_dense(units = 80, activation = "relu", name = "hidden_layer1")

# Second hidden layer: 20 neurons
hidden_layer2 <- hidden_layer1 %>% 
  layer_dense(units = 40, activation = "relu", name = "hidden_layer2")

# Output layer
output <- hidden_layer2 %>% 
  layer_dense(units = 1, activation = "linear", name = "output")

# Define the model
model <- keras_model(inputs = input_all, outputs = output)

# Compile the model
model %>% compile(
  optimizer = "adam",
  loss = "mean_squared_error",
  metrics = c("mean_absolute_error")
)

# Train the model
history <- model %>% fit(
  x = X_all_train_normalized,
  y = y_train,
  epochs = 50,
  batch_size = 32,
  validation_split = 0.2,
  verbose = 1
)

# Evaluate on test set
evaluation <- model %>% evaluate(
  x = X_all_test_normalized,
  y = y_test,
  verbose = 0
)
cat("Neural Network (Fully Connected) - Test MSE:", evaluation[[1]], "\n")
cat("Neural Network (Fully Connected) - Test MAE:", evaluation[[2]], "\n")




#classification problem
y <- bp$hypot_map
y_train <- y[train_idx]
y_test <- y[-train_idx]


library(keras)
# Define the neural network using the functional API
input_all <- layer_input(shape = ncol(X_all_train_normalized), name = "all_input")

# First hidden layer: 100 neurons, fully connected to all inputs
hidden_layer1 <- input_all %>% 
  layer_dense(units = 80, activation = "relu", name = "hidden_layer1")

# Second hidden layer: 20 neurons
hidden_layer2 <- hidden_layer1 %>% 
  layer_dense(units = 40, activation = "relu", name = "hidden_layer2")

# Output layer
output <- hidden_layer2 %>% 
  layer_dense(units = 1, activation = "sigmoid", name = "output")

# Define the model
model <- keras_model(inputs = input_all, outputs = output)

# Compile the model
model %>% compile(
  optimizer = "adam",
  loss = "binary_crossentropy",  # Binary classification loss
  metrics = c("accuracy")
)

# Train the model
history <- model %>% fit(
  x = X_all_train_normalized,
  y = y_train,
  epochs = 50,
  batch_size = 32,
  validation_split = 0.2,
  verbose = 1
)

# Evaluate on test set
# Evaluate on test data
score <- model %>% evaluate(X_all_test_normalized, y_test)
cat("Test Accuracy:", score[[2]])

# Get predicted probabilities
y_pred_prob <- model %>% predict(X_all_test_normalized)

# Convert probabilities to class labels (0 or 1) using a threshold of 0.5
y_pred <- ifelse(y_pred_prob > 0.5, 1, 0)

# Create confusion matrix
conf_matrix <- table(Predicted = y_pred, Actual = y_test)
print(conf_matrix)


library(caret)

# Convert to factor for caret compatibility
y_pred_factor <- factor(y_pred, levels = c(0,1))
y_test_factor <- factor(y_test, levels = c(0,1))

# Generate confusion matrix
confusionMatrix(y_pred_factor, y_test_factor)

library(pROC)
roc_curve <- roc(y_test, y_pred_prob)

# Plot the ROC curve
plot(roc_curve, col = "blue", main = "ROC Curve")
auc(roc_curve)

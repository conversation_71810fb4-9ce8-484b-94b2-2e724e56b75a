bp = read.csv("bp.csv")

library(Amelia)
missmap(bp)

lm_map_arm = lm(MAP..mmHg...Arm. ~ MAP..mmHg...Ankle.,
                data = bp)
summary(lm_map_arm)

lm_map_inv = lm(MAP..mmHg...Arterial. ~ MAP..mmHg...Ankle.,
                data = bp)
summary(lm_map_inv)



lm_sys_arm = lm(Systolic.BP..mmHg...Arm.~Systolic.BP..mmHg...Ankle.,
                data = bp)
summary(lm_sys_arm)

lm_sys_inv = lm(Systolic.BP..mmHg...Arterial.~Systolic.BP..mmHg...Ankle.,
                data = bp)
summary(lm_sys_inv)



lm_dia_arm = lm(Diastolic.BP..mmHg...Arm.~ Diastolic.BP..mmHg...Ankle.,
                data = bp)
summary(lm_dia_arm)

lm_dia_inv = lm(Diastolic.BP..mmHg...Arterial.~Diastolic.BP..mmHg...Ankle.,
                data = bp)
summary(lm_dia_inv)


map_lm = lm(MAP..mmHg...Arterial.~ MAP..mmHg...Ankle. + MAP..mmHg...Arm.,
            data = bp)
summary(map_lm)

map_lm_arm = lm(MAP..mmHg...Arterial. ~ MAP..mmHg...Arm., 
                data=bp)
summary(map_lm_arm)

map_lm_ank = lm(MAP..mmHg...Arterial. ~ MAP..mmHg...Ankle., 
                data = bp)
summary(map_lm_ank)

plot(bp$MAP..mmHg...Ankle.,bp$MAP..mmHg...Arm.)
library(car)
vif(map_lm)

anova(map_lm,map_lm_arm)
anova(map_lm,map_lm_ank)


library(irr)
icc_ank = icc(cbind(bp$MAP..mmHg...Arterial.,
                    bp$MAP..mmHg...Ankle.),
              model = "twoway",type="consistency")
icc_ank

icc_arm = icc(cbind(bp$MAP..mmHg...Arterial.,
                    bp$MAP..mmHg...Arm.),
              model = "twoway",type="consistency")
icc_arm


icc_arm_ank = icc(cbind(bp$MAP..mmHg...Arm.,
                        bp$MAP..mmHg...Ankle.),
                  model = "twoway",type="consistency")
icc_arm_ank
